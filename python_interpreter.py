import pandas as pd
import numpy as np
import ast
import inspect
import re
import sys
import os
import traceback
import time
from typing import Dict, List, Any, Optional, Tuple, Set
from operation import Operation

# Debug logging helper function
def debug_log(message, level=0):
    """Enhanced debug logging with indentation and timestamp"""
    indent = '  ' * level
    timestamp = time.strftime('%H:%M:%S', time.localtime())
    print(f"[DEBUG {timestamp}] {indent}{message}")

class PythonInterpreter:
    """
    A wrapper class for Python interpreter that tracks dataframe operations,
    analyzes code structure, and maintains relationships between dataframes.
    """

    def __init__(self):
        # Initialize the execution namespace with required libraries
        self.namespace = {
            'pd': pd,
            'np': np,
            '__builtins__': __builtins__,
            '__file__': os.path.abspath(__file__)
        }

        # Add matplotlib if available
        try:
            import matplotlib.pyplot as plt
            import matplotlib.font_manager as fm
            import platform

            # Configure matplotlib to support Chinese characters
            if platform.system() == 'Windows':
                plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
            elif platform.system() == 'Darwin':  # macOS
                plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti', 'Heiti TC', 'Arial Unicode MS', 'sans-serif']
            else:  # Linux and others
                plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Droid Sans Fallback', 'sans-serif']

            # Ensure minus sign is displayed correctly
            plt.rcParams['axes.unicode_minus'] = False

            # Add to namespace
            self.namespace['plt'] = plt
            self.namespace['fm'] = fm

            # Try to import mplcursors for cross-hair functionality
            try:
                import mplcursors
                self.namespace['mplcursors'] = mplcursors
            except ImportError:
                try:
                    import pip
                    pip.main(['install', 'mplcursors'])
                    import mplcursors
                    self.namespace['mplcursors'] = mplcursors
                except Exception as e:
                    print(f"Warning: mplcursors not available. Cross-hair functionality will be limited. Error: {str(e)}")

        except ImportError:
            print("Warning: matplotlib not available. Chart functionality will be limited.")

        # Store execution history
        self.history = []

        # Track dataframes and their relationships
        self.dataframes = {}  # name -> pd.DataFrame
        self.dataframe_info = {}  # name -> metadata
        self.relationships = {}  # child -> parent
        self.operations = {}  # df_name -> list of operations

        # Track current operation sequence
        self.current_sequence = []

    def reset(self):
        """Reset the interpreter state while preserving the namespace"""
        self.__init__()

    def execute(self, code: str, namespace=None, script_path=None) -> Dict[str, Any]:
        """
        Execute Python code and track dataframe operations.

        Args:
            code: Python code to execute
            namespace: Optional custom namespace to use for execution. If None, self.namespace is used.
            script_path: Optional path to the script file, used to set __file__ during execution

        Returns:
            Dict with execution results and analysis
        """
        print("\n===== PYTHON INTERPRETER: execute() START =====")
        print('Code:')
        print('```')
        print(code)
        print('```')
        print(f"Code length: {len(code)} characters")
        print(f"Script path: {script_path}")

        # Record code in history
        self.history.append(code)

        # Use provided namespace or default to self.namespace
        ns = namespace if namespace is not None else self.namespace

        # Store initial state for comparison
        initial_vars = set(ns.keys())
        print(f"Initial variables count: {len(initial_vars)}")

        # Pre-analyze code to detect dataframe operations
        print("Pre-analyzing code...")
        analysis = self.analyze_code(code)
        print(f"Analysis results - dataframe operations: {len(analysis.get('dataframe_ops', {}))}")

        # Execute the code
        try:
            # If script_path is provided, temporarily set __file__ to that path
            original_file = ns.get('__file__')
            if script_path is not None:
                ns['__file__'] = script_path
                print(f"Set __file__ to: {script_path}")

            # Execute the code
            print("Executing code...")
            exec(code, ns)
            print("Code execution completed successfully")

            # Restore the original __file__ if needed
            if script_path is not None and original_file is not None:
                ns['__file__'] = original_file

            # Identify new or updated dataframes
            print("Identifying new or updated dataframes...")
            found_dataframes = self._find_dataframes(initial_vars, ns)
            print(f"Found {len(found_dataframes)} dataframes: {list(found_dataframes.keys())}")

            # Update relationships based on code analysis
            print("Updating dataframe relationships...")
            self._update_relationships(analysis, found_dataframes)
            print(f"Relationships after update: {self.relationships}")

            # Generate operation records
            print("Generating operation records...")
            self._generate_operations(analysis, found_dataframes)

            # Print operations summary
            print("\n----- Operations Summary -----")
            for df_name, ops in self.operations.items():
                print(f"DataFrame '{df_name}': {len(ops)} operations")
                for i, op in enumerate(ops):
                    print(f"  {i+1}. {op.get('type', 'unknown')}: {op.get('code', '')[:50]}{'...' if len(op.get('code', '')) > 50 else ''}")

            print("===== PYTHON INTERPRETER: execute() END =====\n")
            return {
                'success': True,
                'dataframes': found_dataframes,
                'relationships': self.relationships,
                'operations': self.operations,
                'analysis': analysis
            }

        except Exception as e:
            # Get detailed error information
            error_info = {
                'type': type(e).__name__,
                'message': str(e),
                'line': self._get_error_line(e, code),
                'traceback': traceback.format_exc()
            }

            return {
                'success': False,
                'error': error_info
            }

    def analyze_code(self, code: str) -> Dict[str, Any]:
        """
        Analyze code to detect dataframe operations and relationships.
        Uses AST parsing for more accurate analysis.

        Args:
            code: Python code to analyze

        Returns:
            Dict with analysis results
        """
        try:
            # Parse code into AST
            tree = ast.parse(code)

            # Results containers
            dataframe_assigns = {}  # name -> creating operation
            dataframe_ops = {}  # name -> list of operations
            parent_child = {}  # child -> parent
            operation_types = {}  # node -> operation type

            # Helper function to get source code for a node
            def get_node_source(node):
                lines = code.split('\n')
                if hasattr(node, 'lineno') and hasattr(node, 'end_lineno'):
                    line_start = node.lineno - 1  # AST is 1-indexed
                    line_end = node.end_lineno - 1 if hasattr(node, 'end_lineno') else line_start

                    if line_start == line_end:
                        return lines[line_start]
                    else:
                        return '\n'.join(lines[line_start:line_end+1])
                return None

            # Visitor to analyze the AST
            class DataFrameVisitor(ast.NodeVisitor):
                # Store reference to the outer scope variables
                def __init__(self, outer_dataframes=None):
                    self.dataframe_ops = dataframe_ops
                    self.dataframe_assigns = dataframe_assigns
                    self.parent_child = parent_child
                    self.operation_types = operation_types
                    # Store reference to any existing dataframes from the interpreter
                    self.outer_dataframes = outer_dataframes or {}

                def _log_operation_detection(self, op_type, source_df, target_name, query=None, line_num=-1):
                    """Helper method for consistent logging of dataframe operation detection"""
                    debug_log(f"AST Analysis: Detected {op_type} operation at line {line_num} from '{source_df}' to '{target_name}'")

                    if query is not None:
                        debug_log(f"Query string: '{query}'", level=1)

                    # Log operation tracking activity
                    if source_df in self.dataframe_ops:
                        debug_log(f"Source dataframe '{source_df}' has {len(self.dataframe_ops[source_df])} operations", level=1)

                    if target_name in self.dataframe_ops:
                        debug_log(f"Target dataframe '{target_name}' has {len(self.dataframe_ops[target_name])} operations", level=1)
                    else:
                        debug_log(f"Creating new operations list for target dataframe '{target_name}'", level=1)

                    debug_log(f"Parent-child relationship established: '{source_df}' -> '{target_name}'", level=1)

                def visit_Assign(self, node):
                    node_source = get_node_source(node)

                    # Case 1: Regular variable assignment (df = ...)
                    if len(node.targets) == 1 and isinstance(node.targets[0], ast.Name):
                        target_name = node.targets[0].id

                        # Detect dataframe creation from CSV
                        if (isinstance(node.value, ast.Call) and
                            isinstance(node.value.func, ast.Attribute) and
                            isinstance(node.value.func.value, ast.Name) and
                            node.value.func.value.id == 'pd' and
                            node.value.func.attr == 'read_csv'):

                            source_path = None
                            if node.value.args:
                                if isinstance(node.value.args[0], ast.Constant):
                                    source_path = node.value.args[0].value
                                elif isinstance(node.value.args[0], ast.Str):  # Python < 3.8
                                    source_path = node.value.args[0].s

                            self.dataframe_assigns[target_name] = {
                                'type': 'csv_import',
                                'source': source_path,
                                'code': node_source
                            }
                            self.operation_types[node] = 'csv_import'
                            print(f"AST Analysis: Detected CSV import for {target_name}")

                        # Detect dataframe query operations (direct query call)
                        elif (isinstance(node.value, ast.Call) and
                              isinstance(node.value.func, ast.Attribute) and
                              node.value.func.attr == 'query'):

                            source_df = self._get_source_df(node.value.func.value)
                            if source_df is not None:
                                # Get the line number from the node
                                line_num = getattr(node, 'lineno', -1)
                                self.parent_child[target_name] = source_df

                                query_string = None
                                if node.value.args:
                                    if isinstance(node.value.args[0], ast.Constant):
                                        query_string = node.value.args[0].value
                                    elif isinstance(node.value.args[0], ast.Str):  # Python < 3.8
                                        query_string = node.value.args[0].s

                                # Use helper method for consistent logging
                                self._log_operation_detection('direct query', source_df, target_name, query_string, line_num)

                                # Create operation info for the query
                                op_info = {
                                    'type': 'query',
                                    'parent': source_df,
                                    'query': query_string,
                                    'code': node_source,
                                    'node': node
                                }

                                # Add operation to source dataframe
                                if source_df not in dataframe_ops:
                                    dataframe_ops[source_df] = []
                                dataframe_ops[source_df].append(op_info)

                                # Also add operation to target dataframe's operations
                                if target_name not in dataframe_ops:
                                    dataframe_ops[target_name] = []
                                dataframe_ops[target_name].append(op_info)

                                dataframe_assigns[target_name] = op_info
                                operation_types[node] = 'query'

                        # Detect query followed by copy() - the pattern used in the script
                        elif (isinstance(node.value, ast.Call) and
                              isinstance(node.value.func, ast.Attribute) and
                              node.value.func.attr == 'copy' and
                              isinstance(node.value.func.value, ast.Call) and
                              isinstance(node.value.func.value.func, ast.Attribute) and
                              node.value.func.value.func.attr == 'query'):

                            # Get the original dataframe the query is performed on
                            query_call = node.value.func.value
                            source_df = self._get_source_df(query_call.func.value)

                            if source_df is not None:
                                # Get the line number from the node
                                line_num = getattr(node, 'lineno', -1)

                                # Extract the query string from the query call's arguments
                                query_string = None
                                if query_call.args:
                                    if isinstance(query_call.args[0], ast.Constant):
                                        query_string = query_call.args[0].value
                                    elif isinstance(query_call.args[0], ast.Str):  # Python < 3.8
                                        query_string = query_call.args[0].s

                                # Use helper method for consistent logging
                                self._log_operation_detection('query+copy', source_df, target_name, query_string, line_num)
                                debug_log(f"Operation detection: dataframe.query().copy() chain pattern", level=1)
                                debug_log(f"AST node type: {node.__class__.__name__}", level=2)
                                debug_log(f"Full operation code: {node_source.strip()}", level=2)
                                parent_child[target_name] = source_df

                                # Create operation info for this query+copy operation
                                op_info = {
                                    'type': 'query',
                                    'parent': source_df,
                                    'query': query_string,
                                    'code': node_source,
                                    'node': node
                                }

                                # Add operation to source dataframe
                                if source_df not in dataframe_ops:
                                    dataframe_ops[source_df] = []
                                dataframe_ops[source_df].append(op_info)

                                # Also add operation to target dataframe's operations
                                if target_name not in dataframe_ops:
                                    dataframe_ops[target_name] = []
                                dataframe_ops[target_name].append(op_info)

                                dataframe_assigns[target_name] = op_info
                                operation_types[node] = 'query'

                        # Other dataframe operations
                        elif self._is_dataframe_operation(node.value):
                            source_df = self._get_source_df(node.value)
                            if source_df is not None:
                                op_type = self._determine_operation_type(node.value)
                                print(f"AST Analysis: Detected {op_type} operation from {source_df} to {target_name}")

                                op_info = {
                                    'type': op_type,
                                    'parent': source_df,
                                    'code': node_source,
                                    'node': node
                                }

                                if source_df not in dataframe_ops:
                                    dataframe_ops[source_df] = []
                                dataframe_ops[source_df].append(op_info)

                                if op_type in ['merge', 'join', 'concat']:
                                    parent_child[target_name] = source_df
                                    dataframe_assigns[target_name] = op_info

                                operation_types[node] = op_type

                    # Case 2: Column assignment (df['col'] = ...)
                    elif len(node.targets) == 1 and isinstance(node.targets[0], ast.Subscript):
                        if isinstance(node.targets[0].value, ast.Name):
                            df_name = node.targets[0].value.id

                            # Try to extract column name
                            col_name = None
                            if isinstance(node.targets[0].slice, ast.Constant):  # Python 3.9+
                                col_name = node.targets[0].slice.value
                            elif hasattr(node.targets[0].slice, 'value'):  # Python 3.8 and below
                                if isinstance(node.targets[0].slice.value, ast.Constant):
                                    col_name = node.targets[0].slice.value.value
                                elif isinstance(node.targets[0].slice.value, ast.Str):
                                    col_name = node.targets[0].slice.value.s

                            # Check if this is a dataframe we're tracking or has been assigned previously
                            if self._is_known_dataframe(df_name):
                                if df_name not in self.dataframe_ops:
                                    self.dataframe_ops[df_name] = []

                                # Add as column_add operation
                                op_info = {
                                    'type': 'column_add',
                                    'parent': df_name,
                                    'column': col_name,
                                    'code': node_source,
                                    'node': node
                                }
                                self.dataframe_ops[df_name].append(op_info)
                                self.operation_types[node] = 'column_add'
                                print(f"AST Analysis: Detected column add/modify for {df_name}['{col_name}']")

                        # Continue visiting child nodes
                    self.generic_visit(node)

                def visit_Expr(self, node):
                    """Visit expression statements to detect operations like df.rename(inplace=True)"""
                    node_source = get_node_source(node)

                    # Check for method calls (e.g., df.rename(..., inplace=True))
                    if isinstance(node.value, ast.Call) and isinstance(node.value.func, ast.Attribute):
                        method_name = node.value.func.attr

                        # Get the dataframe name this method is called on
                        df_name = None
                        if isinstance(node.value.func.value, ast.Name):
                            df_name = node.value.func.value.id

                        # Check if it's a dataframe operation and the dataframe exists
                        if df_name is not None and self._is_known_dataframe(df_name):
                            # Check if this is an inplace operation
                            is_inplace = False
                            for keyword in node.value.keywords:
                                if keyword.arg == 'inplace' and isinstance(keyword.value, ast.Constant) and keyword.value.value is True:
                                    is_inplace = True
                                    break

                            if is_inplace or method_name in ['sort_values', 'reset_index', 'drop', 'fillna', 'replace', 'rename']:
                                print(f"AST Analysis: Detected inplace {method_name} operation on {df_name}")

                                # Create operation info
                                if df_name not in self.dataframe_ops:
                                    self.dataframe_ops[df_name] = []

                                op_info = {
                                    'type': method_name,
                                    'parent': df_name,
                                    'code': node_source,
                                    'inplace': is_inplace,
                                    'node': node
                                }

                                self.dataframe_ops[df_name].append(op_info)
                                self.operation_types[node] = method_name

                    self.generic_visit(node)

                def visit_AugAssign(self, node):
                    # Handle augmented assignments (e.g., df += 1)
                    if isinstance(node.target, ast.Name):
                        target_name = node.target.id
                        node_source = get_node_source(node)

                        if target_name in self.dataframe_assigns or self._is_known_dataframe(target_name):
                            op_info = {
                                'type': 'modify',
                                'parent': target_name,
                                'code': node_source
                            }

                            if target_name not in self.dataframe_ops:
                                self.dataframe_ops[target_name] = []
                            self.dataframe_ops[target_name].append(op_info)
                            self.operation_types[node] = 'modify'

                    self.generic_visit(node)

                def _get_source_df(self, node):
                    """Extract the source dataframe name from a node"""
                    if isinstance(node, ast.Name):
                        return node.id
                    elif isinstance(node, ast.Attribute) and isinstance(node.value, ast.Name):
                        # Handle chained operations: df.query().reset_index()
                        return node.value.id
                    return None

                def _is_dataframe_operation(self, node):
                    """Check if node represents a dataframe operation"""
                    if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
                        # Common dataframe operations
                        df_methods = [
                            # Filtering, sorting, reshaping
                            'query', 'groupby', 'sort_values', 'reset_index', 'set_index',
                            'pivot', 'pivot_table', 'melt', 'stack', 'unstack',

                            # Joining and combining
                            'merge', 'join', 'concat',

                            # Column and row operations
                            'drop', 'dropna', 'fillna', 'replace', 'rename',

                            # Aggregations
                            'agg', 'aggregate', 'apply', 'transform',

                            # Conversions
                            'astype', 'convert_dtypes', 'to_csv', 'to_dict'
                        ]
                        return node.func.attr in df_methods
                    return False

                def _determine_operation_type(self, node):
                    """Determine type of dataframe operation"""
                    if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
                        return node.func.attr
                    return 'unknown'

                def _is_known_dataframe(self, name):
                    """Check if variable name refers to a known dataframe"""
                    return name in self.dataframe_assigns or name in self.outer_dataframes

            # Visit the AST to collect operations and relationships
            visitor = DataFrameVisitor(outer_dataframes=self.dataframes)
            visitor.visit(tree)

            # Additionally, parse the code line by line for operations not caught by AST
            line_operations = self._analyze_code_lines(code)

            return {
                'dataframe_assigns': dataframe_assigns,
                'dataframe_ops': dataframe_ops,
                'parent_child': parent_child,
                'operation_types': operation_types,
                'line_operations': line_operations
            }

        except SyntaxError as e:
            # Handle syntax errors in the code
            return {
                'error': {
                    'type': 'SyntaxError',
                    'message': str(e),
                    'line': e.lineno,
                    'offset': e.offset
                }
            }

    def _analyze_code_lines(self, code: str) -> List[Dict]:
        """
        Analyze code line by line to detect operations not easily caught by AST.
        Mainly used for column operations and modifications.

        Args:
            code: Python code to analyze

        Returns:
            List of detected operations
        """
        operations = []

        # Split code into lines
        lines = code.split('\n')

        for i, line in enumerate(lines):
            line_num = i + 1
            line = line.strip()

            # Skip empty lines and comments
            if not line or line.startswith('#'):
                continue

            # Check for column assignment (df['col'] = ...)
            col_assign_pattern = r'(\w+)\[([\'\"].*?[\'\"])\]\s*='
            matches = re.findall(col_assign_pattern, line)

            for df_name, col_name in matches:
                # Check if df_name is actually a dataframe
                if df_name in self.dataframes or df_name in self.namespace and isinstance(self.namespace[df_name], pd.DataFrame):
                    operations.append({
                        'type': 'column_add',
                        'dataframe': df_name,
                        'column': col_name.strip('\'"'),
                        'line': line_num,
                        'code': line
                    })

            # Check for rename operations
            if '.rename(' in line:
                rename_pattern = r'(\w+)\s*=\s*(\w+)\.rename\('
                matches = re.findall(rename_pattern, line)

                for target, source in matches:
                    operations.append({
                        'type': 'rename',
                        'target': target,
                        'source': source,
                        'line': line_num,
                        'code': line
                    })

            # Check for other common pandas operations
            for op in ['groupby', 'sort_values', 'pivot', 'melt', 'apply']:
                if f'.{op}(' in line:
                    op_pattern = r'(\w+)\s*=\s*(\w+)\.{0}\('.format(op)
                    matches = re.findall(op_pattern, line)

                    for target, source in matches:
                        operations.append({
                            'type': op,
                            'target': target,
                            'source': source,
                            'line': line_num,
                            'code': line
                        })

        return operations

    def _find_dataframes(self, initial_vars: Set[str], namespace=None) -> Dict[str, pd.DataFrame]:
        """
        Identify new or updated dataframes in the namespace.

        Args:
            initial_vars: Set of variables before execution
            namespace: Optional namespace to search in. If None, self.namespace is used.

        Returns:
            Dict of dataframe name -> dataframe object
        """
        print("\n===== PYTHON INTERPRETER: _find_dataframes() =====")
        print(f"Initial variables count: {len(initial_vars)}")

        # Use provided namespace or default to self.namespace
        ns = namespace if namespace is not None else self.namespace
        print(f"Current namespace size: {len(ns)}")

        # Print existing dataframes before finding new ones
        print(f"Existing dataframes before search: {list(self.dataframes.keys())}")

        result = {}
        dataframe_candidates = []

        # First pass: identify potential dataframe variables
        for var_name, var_value in ns.items():
            if isinstance(var_value, pd.DataFrame):
                dataframe_candidates.append(var_name)

        print(f"Found {len(dataframe_candidates)} potential dataframe variables: {dataframe_candidates}")

        # Second pass: filter and process actual new dataframes
        for var_name in dataframe_candidates:
            var_value = ns[var_name]

            # Skip variables that existed before execution and built-in modules
            if var_name in initial_vars or var_name in ['pd', 'np']:
                print(f"Skipping '{var_name}' - existed before script execution or is a module")
                continue

            print(f"Processing new dataframe: '{var_name}', shape: {var_value.shape}")

            # Add to tracked dataframes
            self.dataframes[var_name] = var_value
            result[var_name] = var_value

            # Initialize metadata if needed
            if var_name not in self.dataframe_info:
                self.dataframe_info[var_name] = {
                    'source_path': 'script',
                    'creation_time': pd.Timestamp.now(),
                    'shape': var_value.shape,
                    'dtypes': var_value.dtypes.to_dict()
                }
                print(f"Added metadata for '{var_name}', source_path: {self.dataframe_info[var_name]['source_path']}")
            else:
                print(f"Dataframe '{var_name}' already has metadata, preserving it")

        print(f"\nTotal new dataframes found: {len(result)}")
        print(f"All dataframes after search: {list(self.dataframes.keys())}")
        print("===== _find_dataframes() END =====\n")

        return result

    def _update_relationships(self, analysis: Dict, found_dataframes: Dict[str, pd.DataFrame]):
        """
        Update dataframe relationships based on code analysis.

        Args:
            analysis: Code analysis result
            found_dataframes: Newly found dataframes
        """
        # Update parent-child relationships from analysis
        if 'parent_child' in analysis:
            for child, parent in analysis['parent_child'].items():
                if child in found_dataframes and (parent in self.dataframes or parent in found_dataframes):
                    self.relationships[child] = parent

    def _generate_operations(self, analysis: Dict, found_dataframes: Dict[str, pd.DataFrame]):
        """
        Generate operation records based on code analysis.

        Args:
            analysis: Code analysis result
            found_dataframes: Newly found dataframes
        """
        print("\n===== PYTHON INTERPRETER: _generate_operations() =====")
        print(f"Found dataframes: {list(found_dataframes.keys())}")
        print(f"Existing operations before processing: {len(self.operations)}")
        for df, ops in self.operations.items():
            print(f"  - {df}: {len(ops)} operations")

        operations_added = 0
        dataframes_processed = set()

        # Store all operations first with their line numbers to maintain order
        # Format: (df_name, line_no, operation_info)
        all_operations = []

        # Process dataframe operations from analysis
        if 'dataframe_ops' in analysis:
            df_ops = analysis['dataframe_ops']
            print(f"\nProcessing dataframe operations from analysis... Found {len(df_ops)} dataframes with operations")

            for df_name, ops in df_ops.items():
                print(f"\nProcessing operations for dataframe: '{df_name}'")
                print(f"  Operations found in analysis: {len(ops)}")
                dataframes_processed.add(df_name)

                if df_name not in self.operations:
                    self.operations[df_name] = []
                    print(f"  Created new operations list for '{df_name}'")
                else:
                    print(f"  Dataframe '{df_name}' already has {len(self.operations[df_name])} operations")

                for i, op in enumerate(ops):
                    print(f"  Processing operation {i+1}: {op.get('type', 'unknown')}")

                    operation = {
                        'code': op.get('code', ''),
                        'type': op.get('type', 'unknown'),
                        'timestamp': pd.Timestamp.now(),
                        'source_df': op.get('parent', df_name),
                        'target_df': df_name
                    }

                    # Add descriptive comment based on operation type
                    if op['type'] == 'query':
                        operation['description'] = f"Query: {op.get('query', '')}"
                    elif op['type'] == 'merge':
                        operation['description'] = "Merge dataframes"
                    elif op['type'] == 'column_add':
                        col_name = op.get('column', 'unknown')
                        operation['description'] = f"Add/modify column: {col_name}"
                    else:
                        operation['description'] = f"Operation: {op['type']}"

                    # Get the line number from the node if available, to maintain script order
                    line_no = 999999  # Default high number if we can't determine
                    if 'node' in op and hasattr(op['node'], 'lineno'):
                        line_no = op['node'].lineno
                    else:
                        # Try to extract line number from the code string
                        for line in op.get('code', '').split('\n'):
                            if df_name in line and ('=' in line or '.' in line):
                                # Try to estimate line number by counting newlines in the script up to this match
                                try:
                                    line_no = analysis.get('script', '').split('\n').index(line)
                                    break
                                except ValueError:
                                    pass

                    # Store the operation with its dataframe and line number
                    all_operations.append((df_name, line_no, operation))

            # Sort all operations by line number
            all_operations.sort(key=lambda x: x[1])  # Sort by line_no (the 2nd element in tuple)

            # Now add operations to their respective dataframes in the correct order
            added_operations = set()
            for df_name, line_no, operation in all_operations:
                # Create a unique key for this operation to avoid duplicates
                op_key = (df_name, operation['code'])

                if op_key not in added_operations:
                    # Check if we already have an identical operation
                    operation_exists = False
                    for existing_op in self.operations[df_name]:
                        if existing_op.get('code') == operation['code']:
                            print(f"    Skipping duplicate operation: {operation['code'][:30]}...")
                            operation_exists = True
                            break

                    if not operation_exists:
                        # Set sequence number based on natural order in script
                        # Use line number as the sequence number to preserve script order
                        operation['seq_no'] = line_no

                        self.operations[df_name].append(operation)
                        added_operations.add(op_key)
                        operations_added += 1
                        print(f"    Added new operation (line {line_no}): {operation['description']}")

            # Log results
            for df_name in dataframes_processed:
                print(f"  After processing: {df_name} now has {len(self.operations[df_name])} operations")
        else:
            print("No dataframe operations found in analysis")

        # Process line operations
        line_ops_count = 0
        if 'line_operations' in analysis:
            line_ops = analysis['line_operations']
            print(f"\nProcessing line operations from analysis... Found {len(line_ops)} line operations")

            for op in line_ops:
                df_name = op.get('dataframe', '')
                if df_name and df_name in self.dataframes:
                    print(f"  Processing line operation for dataframe: '{df_name}'")
                    dataframes_processed.add(df_name)

                    if df_name not in self.operations:
                        self.operations[df_name] = []
                        print(f"  Created new operations list for '{df_name}'")

                    operation = {
                        'code': op.get('code', ''),
                        'type': op.get('type', 'unknown'),
                        'timestamp': pd.Timestamp.now(),
                        'source_df': df_name,
                        'target_df': df_name
                    }

                    if op['type'] == 'column_add':
                        col_name = op.get('column', 'unknown')
                        operation['description'] = f"Add/modify column: {col_name}"
                    else:
                        operation['description'] = f"Operation: {op['type']}"

                    # Check if we already have an identical operation
                    operation_exists = False
                    for existing_op in self.operations[df_name]:
                        if existing_op.get('code') == operation['code']:
                            print(f"    Skipping duplicate operation: {operation['code'][:30]}...")
                            operation_exists = True
                            break

                    if not operation_exists:
                        self.operations[df_name].append(operation)
                        operations_added += 1
                        line_ops_count += 1
                        print(f"    Added new operation: {operation['description']}")
        else:
            print("No line operations found in analysis")

        # Check for dataframes that still have no operations
        print("\n----- Checking for dataframes without operations -----")
        for df_name in found_dataframes:
            if df_name not in self.operations:
                print(f"WARNING: Found dataframe '{df_name}' has no operations")
                self.operations[df_name] = []
            elif len(self.operations[df_name]) == 0:
                print(f"WARNING: Dataframe '{df_name}' has empty operations list")

        print(f"\nTotal dataframes processed: {len(dataframes_processed)}")
        print(f"New operations added: {operations_added} (Dataframe ops: {operations_added - line_ops_count}, Line ops: {line_ops_count})")
        print(f"Total operations after processing: {sum(len(ops) for ops in self.operations.values())}")
        print("===== _generate_operations() END =====\n")

    def _get_error_line(self, error: Exception, code: str) -> Optional[int]:
        """Get the line number where an error occurred"""
        if hasattr(error, 'lineno'):
            return error.lineno

        tb = getattr(error, '__traceback__', None)
        if tb:
            # Try to extract line number from traceback
            while tb.tb_next:
                tb = tb.tb_next
            return tb.tb_lineno

        return None

    def get_dataframe(self, name: str) -> Optional[pd.DataFrame]:
        """Get a dataframe by name if it exists"""
        return self.dataframes.get(name)

    def get_all_dataframes(self) -> Dict[str, pd.DataFrame]:
        """Get all tracked dataframes"""
        return self.dataframes.copy()

    def get_relationships(self) -> Dict[str, str]:
        """Get dataframe relationships (child -> parent)"""
        return self.relationships.copy()

    def get_operations(self, df_name: Optional[str] = None) -> Dict[str, List[Dict]]:
        """
        Get operations for a specific dataframe or all dataframes.

        Args:
            df_name: Dataframe name or None for all operations

        Returns:
            Dict of dataframe name -> operations list
        """
        if df_name:
            return {df_name: self.operations.get(df_name, [])}
        return self.operations.copy()

    def get_execution_history(self) -> List[str]:
        """Get history of executed code"""
        return self.history.copy()

    def create_operation_objects(self, app) -> Dict[str, List[Operation]]:
        """
        Convert internal operation records to Operation objects.

        Args:
            app: The main application object with get_next_sequence_number method

        Returns:
            Dict of dataframe name -> list of Operation objects
        """
        result = {}

        # Debug log for operations
        print(f"\n===== PYTHON INTERPRETER: create_operation_objects() =====")
        print(f"Found {len(self.operations)} dataframes with operations")

        for df_name, ops in self.operations.items():
            if df_name not in result:
                result[df_name] = []

            print(f"\nProcessing {len(ops)} operations for dataframe '{df_name}'")

            for i, op in enumerate(ops):
                # Use existing sequence number if available, otherwise get a new one
                if 'seq_no' in op:
                    seq_no = op['seq_no']
                    print(f"  Using preserved sequence number: {seq_no} for operation {i+1}")
                else:
                    seq_no = app.get_next_sequence_number()
                    print(f"  Assigning new sequence number: {seq_no} for operation {i+1}")

                # Create Operation object with preserved sequence number
                operation = Operation(
                    code=op['code'],
                    seq_no=seq_no,
                    source_df=op['source_df'],
                    target_df=op['target_df'],
                    description=op.get('description', 'Operation'),
                    operation_type=op['type']
                )

                print(f"  Added operation: #{seq_no} {op['type']} - {op.get('description', 'Operation')[:30]}...")
                result[df_name].append(operation)

        return result

    def convert_to_pandas_dataframes(self, app) -> Dict[str, Any]:
        """
        Convert raw pandas dataframes to PandasDataFrame objects for the application.

        Args:
            app: The main application object

        Returns:
            Dict of dataframe name -> PandasDataFrame object
        """
        from pandas_dataframe import PandasDataFrame

        result = {}

        # First, create PandasDataFrame objects
        for df_name, df in self.dataframes.items():
            # Get source path from info
            source_path = self.dataframe_info.get(df_name, {}).get('source_path', 'script')

            # Create PandasDataFrame object
            pandas_df = PandasDataFrame(
                data=df,
                name=df_name,
                source_path=source_path
            )

            # Set parent separately after creation if there is a parent relationship
            parent = self.relationships.get(df_name)
            if parent:
                pandas_df.parent = parent

            result[df_name] = pandas_df

        # Then, add operations to each dataframe
        operations = self.create_operation_objects(app)
        for df_name, ops in operations.items():
            if df_name in result:
                result[df_name].operations.extend(ops)

        return result
