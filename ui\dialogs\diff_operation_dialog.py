from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSpinBox, QPushButton, QFormLayout, QLineEdit)
from PyQt6.QtCore import Qt

class DiffOperationDialog(QDialog):
    """
    Dialog for configuring a difference operation on a dataframe column.
    """
    def __init__(self, parent, column_name):
        """
        Initialize the difference operation dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            column_name: The name of the column to apply the difference operation to
        """
        super().__init__(parent)
        self.parent = parent
        self.column_name = column_name
        self.periods = 1
        self.result_column_name = f"{column_name}_diff1"
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle(f"Difference Operation: {self.column_name}")
        self.setMinimumWidth(450)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Column name display with consistent styling
        column_label = QLabel("Column:")
        column_value = QLabel(self.column_name)
        column_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(column_label, column_value)

        # Periods input
        self.periods_spin = QSpinBox()
        self.periods_spin.setRange(1, 10000)
        self.periods_spin.setValue(1)
        self.periods_spin.setSingleStep(1)
        form_layout.addRow("Periods:", self.periods_spin)

        # Result column name preview
        self.result_name_edit = QLineEdit(self.result_column_name)
        form_layout.addRow("Result column name:", self.result_name_edit)

        # Connect periods spin box to update result name
        def update_result_name():
            periods = self.periods_spin.value()
            self.result_name_edit.setText(f"{self.column_name}_diff{periods}")
            
        self.periods_spin.valueChanged.connect(update_result_name)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add description
        description = QLabel("Calculates the difference between consecutive values in the column.")
        description.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(description)

        # Add some spacing
        main_layout.addSpacing(15)

        # Add buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Connect buttons
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.accept)
        
    def accept(self):
        """Handle dialog acceptance and store the values."""
        self.periods = self.periods_spin.value()
        self.result_column_name = self.result_name_edit.text()
        super().accept()
