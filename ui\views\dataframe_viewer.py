from PyQt6.QtWidgets import <PERSON><PERSON>ab<PERSON>idget, QTableView, QHeaderView, QAbstractItemView
from PyQt6.QtCore import Qt, QTimer
from ui.models.lazy_loading_pandas_model import LazyLoadingPandasTableModel

class DataFrameViewer(QTabWidget):
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.setTabsClosable(True)
        self.tabCloseRequested.connect(self.close_tab)
        self.currentChanged.connect(self.on_tab_changed)

    def add_dataframe_tab(self, df_name):
        # Get the PandasDataFrame object from the manager
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)
        if not pandas_df:
            self.main_app.status_bar.showMessage(f"Error: DataFrame {df_name} not found")
            return

        # Create table view with the dataframe's data
        table_view = QTableView()
        table_view.setObjectName(df_name)  # Set object name for later reference

        # Use lazy loading model for better performance with large datasets
        model = LazyLoadingPandasTableModel(pandas_df.data, pandas_df=pandas_df)
        table_view.setModel(model)

        # Store the current viewport state to avoid full recalculation when switching tabs
        table_view.setProperty("viewport_initialized", False)

        # Configure the table view for efficient scrolling and lazy loading
        table_view.setVerticalScrollMode(QAbstractItemView.ScrollMode.ScrollPerPixel)
        table_view.verticalScrollBar().setSingleStep(20)  # Smoother scrolling

        # These settings are crucial for lazy loading to work
        table_view.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        table_view.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Connect scroll events to trigger lazy loading
        scrollbar = table_view.verticalScrollBar()
        scrollbar.valueChanged.connect(
            lambda value: self._on_scroll(table_view, model, value))

        # Connect to the dataLoaded signal to update status
        model.dataLoaded.connect(
            lambda start, end: self._on_data_loaded(df_name, start, end))

        # Enable selection
        table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectItems)
        table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        table_view.customContextMenuRequested.connect(
            lambda pos, name=df_name: self.main_app.op_manager.show_context_menu(pos, name))

        # Make headers clickable for column selection
        header = table_view.horizontalHeader()
        header.setSectionsClickable(True)
        header.sectionClicked.connect(lambda index: table_view.selectColumn(index))

        # Resize columns to content
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        self.addTab(table_view, df_name)
        self.setCurrentIndex(self.count() - 1)  # Switch to the new tab

    def _on_scroll(self, table_view, model, value):
        """
        Handle vertical scroll events to trigger lazy loading.

        Args:
            table_view (QTableView): The table view being scrolled
            model (LazyLoadingPandasTableModel): The model for the table view
            value (int): The new scroll position
        """
        # Get the current visible rows
        visible_rows = model.rowCount()

        # Get the scroll position as a percentage
        scrollbar = table_view.verticalScrollBar()
        max_value = scrollbar.maximum()

        if max_value == 0:  # Avoid division by zero
            return

        scroll_percentage = value / max_value

        # Calculate the approximate row the user has scrolled to
        # This is an approximation since the exact row depends on row heights and view size
        estimated_current_row = int(visible_rows * scroll_percentage)

        # Only load more data when we're near the end of the currently loaded rows
        # We want to load more when the user is about 50 rows away from the end
        threshold_row = visible_rows - 50

        if estimated_current_row > threshold_row and visible_rows >= model._chunk_size:
            print(f"Approaching end of loaded data (row ~{estimated_current_row} of {visible_rows}), requesting more data")
            model.loadMoreData()

    def _on_data_loaded(self, df_name, start_row, end_row):
        """
        Handle the dataLoaded signal from the model.

        Args:
            df_name (str): Name of the dataframe
            start_row (int): First row that was loaded
            end_row (int): Last row that was loaded
        """
        # Get the total number of rows in the dataframe
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)
        if pandas_df:
            total_rows = len(pandas_df.data)
            # Update status bar with loading information
            percentage = min(100, int((end_row / total_rows) * 100))
            self.main_app.status_bar.showMessage(
                f"DataFrame {df_name}: Loaded rows {start_row+1}-{end_row} of {total_rows} ({percentage}%)")

    def close_tab(self, index):
        df_name = self.tabText(index)

        # Ask the manager to remove the dataframe
        if self.main_app.df_manager.remove_dataframe(df_name):
            self.main_app.status_bar.showMessage(f"Removed DataFrame {df_name}")
        else:
            self.main_app.status_bar.showMessage(f"Error: Could not remove DataFrame {df_name}")

        # Remove the tab regardless of whether the dataframe was found
        self.removeTab(index)

    def on_tab_changed(self, index):
        """Optimize rendering when switching tabs"""
        if index < 0:
            return

        current_tab = self.widget(index)
        if current_tab:
            # Check if this is the first time viewing this tab
            if not current_tab.property("viewport_initialized"):
                # Delay full rendering until tab is visible
                self.main_app.status_bar.showMessage(f"Initializing view for {self.tabText(index)}...")
                QTimer.singleShot(50, lambda: self._initialize_viewport(current_tab))
            else:
                # For already initialized tabs, just update status
                model = current_tab.model()
                if hasattr(model, '_visible_rows') and hasattr(model, '_full_data'):
                    rows_loaded = model._visible_rows
                    total_rows = len(model._full_data)
                    self.main_app.status_bar.showMessage(
                        f"Loaded {rows_loaded} of {total_rows} rows ({int((rows_loaded/total_rows)*100)}%)")

    def _initialize_viewport(self, table_view):
        """Initialize viewport for better performance"""
        table_view.setProperty("viewport_initialized", True)

        # Get the model and dataframe name
        model = table_view.model()
        df_name = table_view.objectName()

        # Only load minimal data initially
        if hasattr(model, '_allow_auto_fetch'):
            # Temporarily enable auto-fetch to load just enough data
            model._allow_auto_fetch = True

            # Limit initial load to just what's visible
            visible_height = table_view.viewport().height()
            row_height = table_view.rowHeight(0) if table_view.model().rowCount() > 0 else 20
            visible_rows = max(20, int(visible_height / row_height))

            # Adjust chunk size for initial load if needed
            original_chunk_size = model._chunk_size
            if visible_rows < original_chunk_size:
                model._chunk_size = visible_rows

            # Load just enough data for the visible area
            model.loadMoreData()

            # Restore original chunk size for subsequent loads
            model._chunk_size = original_chunk_size

            # Update status
            rows_loaded = model._visible_rows
            total_rows = len(model._full_data)
            self.main_app.status_bar.showMessage(
                f"DataFrame {df_name}: Loaded {rows_loaded} of {total_rows} rows ({int((rows_loaded/total_rows)*100)}%)")
