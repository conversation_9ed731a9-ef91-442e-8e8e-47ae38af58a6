from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                            QListWidget, QPushButton, QDialogButtonBox, QComboBox, 
                            QListWidgetItem, QSpinBox, QTabWidget, QWidget)
from PyQt6.QtCore import Qt


class ChartDialog(QDialog):
    """Dialog for creating charts with tabs for different chart types"""
    def __init__(self, parent, df_name=None, selected_column_index=None):
        super().__init__(parent)
        self.parent = parent
        self.df_name = df_name
        self.chart_type = "line"  # Default chart type
        self.x_column = ""
        self.y_column = ""  # For scatter charts
        self.y_columns = []  # For line charts
        self.bins = 30  # For histograms
        self.title = f"{df_name} - {self.chart_type} chart" if df_name else ""
        self.selected_column_index = selected_column_index  # Store the selected column index

        # Get dataframe columns for UI elements
        self.columns = []
        if self.df_name:
            pandas_df = self.parent.df_manager.get_dataframe(self.df_name)
            if pandas_df is not None:
                self.columns = list(pandas_df.data.columns)
                
                # If a column is selected, use it as the default column
                if (self.selected_column_index is not None and 
                    0 <= self.selected_column_index < len(self.columns)):
                    self.x_column = self.columns[self.selected_column_index]

        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Create Chart")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)

        main_layout = QVBoxLayout()

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs for each chart type
        self.line_tab = QWidget()
        self.scatter_tab = QWidget()
        self.hist_tab = QWidget()

        # Set up each tab
        self.setup_line_tab()
        self.setup_scatter_tab()
        self.setup_hist_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.line_tab, "Line Chart")
        self.tab_widget.addTab(self.scatter_tab, "Scatter Chart")
        self.tab_widget.addTab(self.hist_tab, "Histogram")

        # Connect tab change to update chart type
        self.tab_widget.currentChanged.connect(self.update_chart_type)

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

        # Common title field
        title_layout = QHBoxLayout()
        title_label = QLabel("Chart Title:")
        self.title_edit = QLineEdit(self.title)
        title_layout.addWidget(title_label)
        title_layout.addWidget(self.title_edit)
        main_layout.addLayout(title_layout)

        # Dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

        self.setLayout(main_layout)

    def setup_line_tab(self):
        """Set up the line chart tab"""
        layout = QVBoxLayout()

        # X-axis column selection
        x_layout = QHBoxLayout()
        x_label = QLabel("X-axis Column:")
        self.line_x_combo = QComboBox()
        self.line_x_combo.addItems(self.columns)
        
        # Set default X-axis column if one is selected
        if self.x_column and self.x_column in self.columns:
            index = self.line_x_combo.findText(self.x_column)
            if index >= 0:
                self.line_x_combo.setCurrentIndex(index)
        
        x_layout.addWidget(x_label)
        x_layout.addWidget(self.line_x_combo)
        layout.addLayout(x_layout)

        # Y-axis columns selection (multiple columns)
        y_label = QLabel("Y-axis Columns (select multiple):")
        layout.addWidget(y_label)

        self.line_y_list = QListWidget()
        self.line_y_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        for col in self.columns:
            item = QListWidgetItem(col)
            self.line_y_list.addItem(item)
        layout.addWidget(self.line_y_list)

        self.line_tab.setLayout(layout)

    def setup_scatter_tab(self):
        """Set up the scatter chart tab"""
        layout = QVBoxLayout()

        # X-axis column selection
        x_layout = QHBoxLayout()
        x_label = QLabel("X-axis Column:")
        self.scatter_x_combo = QComboBox()
        self.scatter_x_combo.addItems(self.columns)
        
        # Set default X-axis column if one is selected
        if self.x_column and self.x_column in self.columns:
            index = self.scatter_x_combo.findText(self.x_column)
            if index >= 0:
                self.scatter_x_combo.setCurrentIndex(index)
        
        x_layout.addWidget(x_label)
        x_layout.addWidget(self.scatter_x_combo)
        layout.addLayout(x_layout)

        # Y-axis column selection
        y_layout = QHBoxLayout()
        y_label = QLabel("Y-axis Column:")
        self.scatter_y_combo = QComboBox()
        self.scatter_y_combo.addItems(self.columns)
        
        # Set default Y-axis column if one is selected (use the next column if available)
        if self.x_column and self.x_column in self.columns:
            current_index = self.columns.index(self.x_column)
            if current_index < len(self.columns) - 1:
                self.scatter_y_combo.setCurrentIndex(current_index + 1)
        
        y_layout.addWidget(y_label)
        y_layout.addWidget(self.scatter_y_combo)
        layout.addLayout(y_layout)

        # Add some spacing
        layout.addStretch()

        self.scatter_tab.setLayout(layout)

    def setup_hist_tab(self):
        """Set up the histogram tab"""
        layout = QVBoxLayout()

        # X-axis column selection
        x_layout = QHBoxLayout()
        x_label = QLabel("Column to plot:")
        self.hist_x_combo = QComboBox()
        self.hist_x_combo.addItems(self.columns)
        
        # Set default column if one is selected
        if self.x_column and self.x_column in self.columns:
            index = self.hist_x_combo.findText(self.x_column)
            if index >= 0:
                self.hist_x_combo.setCurrentIndex(index)
        
        x_layout.addWidget(x_label)
        x_layout.addWidget(self.hist_x_combo)
        layout.addLayout(x_layout)

        # Number of bins
        bins_layout = QHBoxLayout()
        bins_label = QLabel("Number of bins:")
        self.bins_spinbox = QSpinBox()
        self.bins_spinbox.setRange(5, 100)
        self.bins_spinbox.setValue(self.bins)
        self.bins_spinbox.setSingleStep(5)
        bins_layout.addWidget(bins_label)
        bins_layout.addWidget(self.bins_spinbox)
        layout.addLayout(bins_layout)

        # Add some spacing
        layout.addStretch()

        self.hist_tab.setLayout(layout)

    def update_chart_type(self, index):
        """Update chart type based on selected tab"""
        if index == 0:
            self.chart_type = "line"
        elif index == 1:
            self.chart_type = "scatter"
        elif index == 2:
            self.chart_type = "hist"

        # Update title if it hasn't been modified
        if not self.title_edit.isModified():
            self.title = f"{self.df_name} - {self.chart_type} chart"
            self.title_edit.setText(self.title)

    def accept(self):
        """Handle dialog acceptance"""
        # Get common data
        self.title = self.title_edit.text()

        # Get data specific to the selected chart type
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # Line chart
            self.chart_type = "line"
            self.x_column = self.line_x_combo.currentText()
            self.y_columns = [item.text() for item in self.line_y_list.selectedItems()]
            self.y_column = ""  # Not used for line chart
        elif current_tab == 1:  # Scatter chart
            self.chart_type = "scatter"
            self.x_column = self.scatter_x_combo.currentText()
            self.y_column = self.scatter_y_combo.currentText()
            self.y_columns = []  # Not used for scatter chart
        elif current_tab == 2:  # Histogram
            self.chart_type = "hist"
            self.x_column = self.hist_x_combo.currentText()
            self.bins = self.bins_spinbox.value()
            self.y_column = ""  # Not used for histogram
            self.y_columns = []  # Not used for histogram

        super().accept()
