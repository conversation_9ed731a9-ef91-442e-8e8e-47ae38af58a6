import sys
import pandas as pd
import numpy as np
import os
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QToolBar, QStatusBar
from PyQt6.QtGui import QIcon
from file_manager import <PERSON>Manager
from operation_manager import OperationManager
from code_manager import CodeManager
from ui.views.dataframe_viewer import DataFrameViewer
from ui.models.lazy_loading_pandas_model import LazyLoadingPandasTableModel
from pandas_dataframe_manager import PandasDataFrameManager

class PandasDesktopApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Pandas Desktop")
        self.global_seq_no = 1  # Initialize global sequence number

        # Create the dataframe manager
        self.df_manager = PandasDataFrameManager(self)

        # For backward compatibility with existing code, expose dataframes through property
        # Use object.__setattr__ to bypass our custom __setattr__ method
        object.__setattr__(self, "_dataframes", self.df_manager.dataframes)

        self.setup_ui()

    def __setattr__(self, name, value):
        """Custom attribute setter to prevent direct access to _dataframes

        Raises:
            AttributeError: If attempting to set _dataframes directly
        """
        if name == "_dataframes" and hasattr(self, "_dataframes"):
            import inspect
            caller_frame = inspect.currentframe().f_back
            caller_class = caller_frame.f_locals.get('self', None).__class__.__name__ if 'self' in caller_frame.f_locals else None
            caller_function = caller_frame.f_code.co_name

            raise AttributeError(
                f"Direct modification of PandasDesktopApp._dataframes from {caller_class}.{caller_function} is not allowed. "
                f"Use df_manager methods instead."
            )

        # Default behavior for other attributes
        super().__setattr__(name, value)

    def get_next_sequence_number(self):
        """Get the next global sequence number and increment it"""
        seq_no = self.global_seq_no
        self.global_seq_no += 1
        return seq_no

    @property
    def dataframes(self):
        """Property to access dataframes from the manager for backward compatibility

        Raises:
            DeprecationWarning: Direct access to dataframes property is deprecated.
                                Use df_manager methods instead.
        """
        import inspect
        caller_frame = inspect.currentframe().f_back
        caller_class = caller_frame.f_locals.get('self', None).__class__.__name__ if 'self' in caller_frame.f_locals else None
        caller_function = caller_frame.f_code.co_name

        # Allow access only from PandasDesktopApp methods for backward compatibility
        if caller_class != 'PandasDesktopApp':
            raise DeprecationWarning(
                f"Direct access to PandasDesktopApp.dataframes from {caller_class}.{caller_function} is deprecated. "
                f"Use df_manager methods instead (get_dataframe, get_all_dataframes, etc.)."
            )

        return self._dataframes

    def setup_ui(self):
        # Set window size
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Initialize components
        self.file_manager = FileManager(self)
        self.df_viewer = DataFrameViewer(self)
        self.op_manager = OperationManager(self)
        self.code_manager = CodeManager(self)

        # Create toolbar
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)

        # Add load CSV button
        load_csv_btn = QPushButton("Load CSV")
        load_csv_btn.clicked.connect(self.file_manager.load_csv)
        toolbar.addWidget(load_csv_btn)

        # Add code manager button
        view_code_btn = QPushButton("Code Manager")
        view_code_btn.clicked.connect(lambda: self.code_manager.show_manager())
        toolbar.addWidget(view_code_btn)

        # Add undo button
        undo_btn = QPushButton("Undo")
        undo_btn.clicked.connect(self.undo_last_operation)
        toolbar.addWidget(undo_btn)

        # Add DataFrame viewer to main layout
        main_layout.addWidget(self.df_viewer)

        # Add status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def undo_last_operation(self):
        """
        Undo the last operation performed on any dataframe by removing the operation with the highest sequence
        number and rebuilding the dataframe from its original data source and remaining operations.

        This function updates the GUI and code manager accordingly. If no operations are available,
        or if the dataframe lacks a source path, an appropriate message is displayed on the status bar.
        """
        # Find the dataframe with the operation that has the highest sequence number
        latest_seq_no = 0
        df_with_latest_op = None
        latest_operation = None
        latest_op_index = -1

        # Search through all dataframes to find the operation with the highest sequence number
        for df_name, pandas_df in self.df_manager.get_all_dataframes().items():
            if not pandas_df.operations:
                continue

            for i, op in enumerate(pandas_df.operations):
                if hasattr(op, 'seq_no') and op.seq_no > latest_seq_no:
                    latest_seq_no = op.seq_no
                    df_with_latest_op = df_name
                    latest_operation = op
                    latest_op_index = i

        # If no operations found in any dataframe
        if df_with_latest_op is None:
            self.status_bar.showMessage("No operations to undo")
            return

        try:
            # Get the dataframe with the latest operation
            pandas_df = self.df_manager.get_dataframe(df_with_latest_op)

            # Remove the latest operation
            pandas_df.operations.pop(latest_op_index)

            # Recreate the dataframe from scratch with all operations except the removed one
            if pandas_df.source_path != "unknown":
                # Reload the original data
                original_data = pd.read_csv(pandas_df.source_path)

                # Create a new dataframe with the original data
                new_df = pd.DataFrame(original_data)

                # Apply all remaining operations
                for operation in pandas_df.operations:
                    try:
                        # Create a local namespace for exec
                        local_vars = {"df": new_df, "pd": pd, "np": np}

                        # Execute the operation code
                        if hasattr(operation, 'code'):
                            op_code = operation.code
                        else:
                            op_code = operation

                        exec(op_code, {"pd": pd, "np": np}, local_vars)
                    except Exception as op_error:
                        self.status_bar.showMessage(f"Error applying operation: {str(op_error)}")

                # Update the dataframe data
                pandas_df.data = new_df

                # Update the view if this is the currently displayed dataframe
                current_index = self.df_viewer.currentIndex()
                if current_index >= 0 and self.df_viewer.tabText(current_index) == df_with_latest_op:
                    current_tab = self.df_viewer.currentWidget()
                    # Use LazyLoadingPandasTableModel for better performance with large datasets
                    current_tab.setModel(LazyLoadingPandasTableModel(pandas_df.data))

                # Update the code manager
                self.code_manager.update_code(df_with_latest_op)

                # Get operation description if available
                op_description = getattr(latest_operation, 'description', str(latest_operation))
                self.status_bar.showMessage(f"Undid operation: {op_description} (#{latest_seq_no})")
            else:
                self.status_bar.showMessage("Cannot undo operations on dataframes without source path")
        except Exception as e:
            self.status_bar.showMessage(f"Error undoing operation: {str(e)}")
            # Restore the operation if there was an error
            if 'latest_operation' in locals() and latest_op_index != -1:
                pandas_df.operations.insert(latest_op_index, latest_operation)

# Make sure DataFrameViewer has a close_tab method
def add_close_tab_method():
    if not hasattr(DataFrameViewer, 'close_tab'):
        def close_tab(self, index):
            self.removeTab(index)
        DataFrameViewer.close_tab = close_tab

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set application icon
    icon_path = os.path.join(os.path.dirname(__file__), 'ui', 'img', 'desktop_pandas.png')
    if os.path.exists(icon_path):
        app_icon = QIcon(icon_path)
        app.setWindowIcon(app_icon)

        # For Windows: Set app ID to ensure taskbar icon is properly displayed
        try:
            import ctypes
            app_id = 'desktop_pandas.app'  # Arbitrary string, but should be unique
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
        except Exception as e:
            print(f"Note: Could not set app ID for taskbar icon: {e}")

    add_close_tab_method()
    window = PandasDesktopApp()
    window.show()
    sys.exit(app.exec())
