import pandas as pd
# Load original data from E:/git/futunn_trader/site_specific/futunn/research/HK_800700_5min_candles.csv
df = pd.read_csv('E:/git/futunn_trader/site_specific/futunn/research/HK_800700_5min_candles.csv')

# Applied operations:
df['time_date'] = pd.to_datetime(df['time']).dt.date
df['time_hour'] = pd.to_datetime(df['time']).dt.hour
df['time_minute'] = pd.to_datetime(df['time']).dt.minute
df['time_hour_*60.0'] = df['time_hour'] * 60.0
df['time_hour_*60.0_+_time_minute'] = df['time_hour_*60.0'] + df['time_minute']
df.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)