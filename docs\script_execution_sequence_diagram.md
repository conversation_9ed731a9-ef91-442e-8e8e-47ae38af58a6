# Script Execution Sequence Diagram

This diagram illustrates the sequence of operations when executing a script in the Desktop Pandas application.

```mermaid
sequenceDiagram
    participant User
    participant CodeManager
    participant MainApp
    participant PythonInterpreter
    participant PandasDataFrame
    participant UIComponents
    
    User->>CodeManager: Initiate script execution
    CodeManager->>CodeManager: Get code from text editor
    CodeManager->>CodeManager: Save as current_script
    CodeManager->>CodeManager: Create namespace with pd, np
    CodeManager->>PythonInterpreter: exec(code, namespace)
    
    PythonInterpreter-->>CodeManager: Return execution results
    
    Note over CodeManager: Process results
    
    CodeManager->>CodeManager: Analyze for DataFrames
    CodeManager->>CodeManager: Detect parent-child relationships
    
    loop For each DataFrame found
        alt DataFrame already exists
            CodeManager->>PandasDataFrame: Update with new data
            CodeManager->>CodeManager: Update parent if needed
            CodeManager->>CodeManager: Add new operations
            CodeManager->>UIComponents: Refresh view
        else DataFrame is new
            CodeManager->>PandasDataFrame: Create new PandasDataFrame
            CodeManager->>PandasDataFrame: Set parent attribute
            CodeManager->>PandasDataFrame: Add operations from script
            CodeManager->>MainApp: Add to dataframes dictionary
            CodeManager->>UIComponents: Create new tab
        end
    end
    
    alt DataFrames found
        CodeManager->>CodeManager: save_script_state()
        CodeManager->>MainApp: Show success message
    else No DataFrames
        CodeManager->>MainApp: Show "no DataFrames created" message
    end
    
    opt Error occurs
        PythonInterpreter-->>CodeManager: Raise exception
        CodeManager->>MainApp: Show error message
    end
```

## Description

This sequence diagram shows:

1. How script execution is initiated in the CodeManager
2. The execution flow through the Python interpreter
3. How the results are processed to find and handle DataFrames
4. The different paths for updating existing DataFrames versus creating new ones
5. How operations are tracked and attached to DataFrames
6. The error handling mechanism
7. How the UI is updated with the results

Created on: 2025-05-09
