from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QComboBox, QPushButton, QLineEdit, QFormLayout)
from PyQt6.QtCore import Qt

class GroupbyTransformDialog(QDialog):
    """
    Dialog for configuring a groupby transform operation on a dataframe column.
    """
    def __init__(self, parent, groupby_column_name, dataframe_columns):
        """
        Initialize the groupby transform dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            groupby_column_name: The name of the column to group by
            dataframe_columns: List of column names in the dataframe
        """
        super().__init__(parent)
        self.parent = parent
        self.groupby_column_name = groupby_column_name
        self.dataframe_columns = dataframe_columns
        self.transform_column = ""
        self.transform_func = "first"
        self.lambda_expr = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle(f"GroupBy Transform: {self.groupby_column_name}")
        self.setMinimumWidth(450)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Group by column display with consistent styling
        group_by_label = QLabel("GroupBy column:")
        group_by_value = QLabel(self.groupby_column_name)
        group_by_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(group_by_label, group_by_value)

        # Add transform column selection
        self.transform_column_combo = QComboBox()
        self.transform_column_combo.addItems(self.dataframe_columns)
        form_layout.addRow("Transform column:", self.transform_column_combo)

        # Add transform function selection
        self.transform_func_combo = QComboBox()
        self.transform_func_combo.addItems(['first', 'last', 'lambda'])
        form_layout.addRow("Transform function:", self.transform_func_combo)

        # Add lambda expression input
        self.lambda_input = QLineEdit()
        self.lambda_input.setPlaceholderText("e.g., lambda x: x.mean()")
        self.lambda_input.setEnabled(False)  # Initially disabled
        form_layout.addRow("Lambda expression:", self.lambda_input)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add some spacing
        main_layout.addSpacing(15)

        # Connect function combo to enable/disable lambda input
        def update_lambda_input():
            self.lambda_input.setEnabled(self.transform_func_combo.currentText() == 'lambda')

        self.transform_func_combo.currentTextChanged.connect(update_lambda_input)

        # Add buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Connect buttons
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.accept)
        
    def accept(self):
        """Handle dialog acceptance and store the values."""
        self.transform_column = self.transform_column_combo.currentText()
        self.transform_func = self.transform_func_combo.currentText()
        
        if self.transform_func == 'lambda':
            self.lambda_expr = self.lambda_input.text()
            if not self.lambda_expr:
                self.parent.status_bar.showMessage("Error: Lambda expression cannot be empty")
                return
                
        super().accept()
        
    def get_new_column_name(self):
        """Get the name for the new column that will be created."""
        return f"{self.transform_column}_groupby_{self.groupby_column_name}"
        
    def get_operation_code(self, var_name):
        """
        Get the operation code for the groupby transform.
        
        Args:
            var_name: The variable name for the dataframe
            
        Returns:
            The operation code as a string
        """
        new_column_name = self.get_new_column_name()
        
        if self.transform_func == 'lambda':
            return f"{var_name}['{new_column_name}'] = {var_name}.groupby('{self.groupby_column_name}')['{self.transform_column}'].transform({self.lambda_expr})"
        else:
            return f"{var_name}['{new_column_name}'] = {var_name}.groupby('{self.groupby_column_name}')['{self.transform_column}'].transform('{self.transform_func}')"
