from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QDialogButtonBox,
                            QHBoxLayout, QTableView, QHeaderView)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
import pandas as pd
from ui.models.pandas_model import PandasTableModel

class InfoDialog(QDialog):
    """Dialog for displaying dataframe information (dtypes and count)"""
    def __init__(self, parent, df_name=None):
        super().__init__(parent)
        self.parent = parent
        self.df_name = df_name

        # Get dataframe information
        self.df_info = self.get_dataframe_info()

        self.setup_ui()

    def get_dataframe_info(self):
        """Get dataframe information (dtypes and count)"""
        info = {}

        if not self.df_name:
            return info

        # Get the dataframe from the manager
        pandas_df = self.parent.df_manager.get_dataframe(self.df_name)
        if not pandas_df:
            return info

        # Get the original column order from the dataframe
        columns = pandas_df.data.columns.tolist()

        # Create lists to store information for each column in the original order
        column_names = []
        dtypes_list = []
        count_list = []
        null_count_list = []
        memory_usage_list = []

        # Collect information for each column in the original order
        for col in columns:
            column_names.append(col)
            dtypes_list.append(str(pandas_df.data[col].dtype))
            count_list.append(pandas_df.data[col].count())
            null_count_list.append(pandas_df.data[col].isna().sum())
            memory_usage_list.append(pandas_df.data[col].memory_usage(deep=True))

        # Create a DataFrame with all the information in the original column order
        info_df = pd.DataFrame({
            'Column Name': column_names,
            'Data Type': dtypes_list,
            'Non-Null Count': count_list,
            'Null Count': null_count_list,
            'Memory Usage (bytes)': memory_usage_list
        })

        # Store the info DataFrame
        info['info_df'] = info_df

        # Get shape information
        info['shape'] = f"Shape: {pandas_df.data.shape[0]} rows × {pandas_df.data.shape[1]} columns"

        # Get total memory usage
        info['memory'] = f"Memory usage: {pandas_df.data.memory_usage(deep=True).sum() / (1024 * 1024):.2f} MB"

        return info

    def setup_ui(self):
        """Set up the dialog UI"""
        self.setWindowTitle(f"DataFrame Info: {self.df_name}")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        main_layout = QVBoxLayout()

        # Add header with dataframe name
        header_layout = QHBoxLayout()
        header_label = QLabel(f"DataFrame: {self.df_name}")
        header_font = QFont()
        header_font.setBold(True)
        header_font.setPointSize(12)
        header_label.setFont(header_font)
        header_layout.addWidget(header_label)
        header_layout.addStretch()
        main_layout.addLayout(header_layout)

        # Add shape and memory information
        if 'shape' in self.df_info:
            shape_label = QLabel(self.df_info['shape'])
            main_layout.addWidget(shape_label)

        if 'memory' in self.df_info:
            memory_label = QLabel(self.df_info['memory'])
            main_layout.addWidget(memory_label)

        # Add separator
        separator = QLabel()
        separator.setFrameShape(QLabel.Shape.HLine)
        separator.setFrameShadow(QLabel.Shadow.Sunken)
        main_layout.addWidget(separator)

        # Add table with column information
        if 'info_df' in self.df_info:
            info_label = QLabel("Column Information:")
            info_label.setFont(QFont("", weight=QFont.Weight.Bold))
            main_layout.addWidget(info_label)

            # Create table view
            table_view = QTableView()
            table_view.setAlternatingRowColors(True)

            # Disable sorting
            table_view.setSortingEnabled(False)

            # Create model with the info DataFrame
            model = PandasTableModel(self.df_info['info_df'])
            table_view.setModel(model)

            # Configure the table view
            table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
            table_view.horizontalHeader().setStretchLastSection(True)
            table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)

            # Ensure header is not clickable for sorting
            table_view.horizontalHeader().setSectionsClickable(False)

            # Add the table view to the layout
            main_layout.addWidget(table_view)

        # Add dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

        self.setLayout(main_layout)
