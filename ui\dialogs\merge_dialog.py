from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                            QListWidget, QPushButton, QDialogButtonBox, QComboBox, 
                            QCheckBox, QMessageBox, QListWidgetItem, QFrame, QGridLayout, QWidget)
from PyQt6.QtCore import Qt

class MergeDialog(QDialog):
    def __init__(self, parent, available_dataframes=None, current_df_name=None):
        super().__init__(parent)
        self.parent = parent
        self.available_dataframes = available_dataframes or []
        self.current_df_name = current_df_name
        self.selected_dataframe = None
        self.how = "inner"
        self.left_on = ""
        self.right_on = ""
        self.suffixes = ("_x", "_y")
        self.indicator = False
        self.selected_columns = []  # Store selected columns from right dataframe
        self.right_df_columns = []  # Store all columns from right dataframe
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Merge DataFrames")
        self.setMinimumWidth(900)
        self.setMinimumHeight(650)

        # Main layout is vertical
        main_layout = QVBoxLayout()

        # Title and description in a panel
        title_panel = QVBoxLayout()

        title_label = QLabel("Merge DataFrames")
        title_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #333333;")
        title_panel.addWidget(title_label)

        description_label = QLabel("Select dataframes and join options to create a merged dataframe")
        description_label.setStyleSheet("color: #666666;")
        title_panel.addWidget(description_label)

        # Create a widget for the title panel
        title_panel_widget = QWidget()
        title_panel_widget.setLayout(title_panel)
        title_panel_widget.setStyleSheet("background-color: #ffffff; border-radius: 3px; padding: 5px;")

        main_layout.addWidget(title_panel_widget)

        # Add a horizontal line separator
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(line)

        # Middle section with left, join type, and right dataframe panels
        middle_section = QHBoxLayout()
        middle_section.setObjectName("middle_section")

        # Left dataframe panel
        left_panel = QVBoxLayout()
        left_panel.setSpacing(5)  # Reduce spacing between elements

        # Left dataframe header with two rows
        left_header_layout = QVBoxLayout()
        left_header_layout.setSpacing(2)  # Reduce spacing between header elements

        left_label = QLabel("Left")
        left_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #444444;")
        left_header_layout.addWidget(left_label)

        # # Create a grid layout for the dataframe display
        # df_display_grid = QGridLayout()
        # df_display_grid.setSpacing(4)  # Reduce spacing

        # left_header_layout.addLayout(df_display_grid)
        left_panel.addLayout(left_header_layout)

        # Add minimal spacing
        left_panel.addSpacing(5)

        # Create a grid layout for the form fields
        left_grid_layout = QGridLayout()
        left_grid_layout.setSpacing(4)  # Reduce spacing in grid layout
        left_grid_layout.setContentsMargins(0, 0, 0, 0)  # Remove margins

        left_df_label = QLabel("Dataframe:")

        # Create a read-only line edit to display the dataframe name
        # This makes it visually consistent with the right panel's combo box
        left_df_display = QLineEdit(self.current_df_name)
        left_df_display.setReadOnly(True)
        left_df_display.setStyleSheet("""
            QLineEdit {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 2px;
                color: #666666;
            }
        """)

        left_grid_layout.addWidget(left_df_label, 0, 0)
        left_grid_layout.addWidget(left_df_display, 0, 1)
        # df_display_grid.setColumnStretch(1, 1)

        # Join on columns
        join_on_label = QLabel("Join on columns:")
        self.left_on_edit = QLineEdit()
        self.left_on_edit.setStyleSheet("background-color: white; border: 1px solid #cccccc; padding: 2px;")
        self.left_on_edit.setMinimumWidth(150)  # Set minimum width for consistency
        left_grid_layout.addWidget(join_on_label, 1, 0)
        left_grid_layout.addWidget(self.left_on_edit, 1, 1)

        # Column suffix
        suffix_label = QLabel("Column suffix:")
        left_suffix_edit = QLineEdit("_x")
        left_suffix_edit.setStyleSheet("background-color: white; border: 1px solid #cccccc; padding: 2px;")
        left_suffix_edit.setMinimumWidth(150)  # Set minimum width for consistency
        left_grid_layout.addWidget(suffix_label, 2, 0)
        left_grid_layout.addWidget(left_suffix_edit, 2, 1)

        # Make the second column (fields) expand
        left_grid_layout.setColumnStretch(1, 1)

        left_panel.addLayout(left_grid_layout)

        # Add a placeholder widget to match the column selection in the right panel
        placeholder_label = QLabel("Columns are fixed for left dataframe")
        placeholder_label.setStyleSheet("color: #888888; font-style: italic; margin-top: 5px;")
        left_panel.addWidget(placeholder_label)

        # Add stretch to push everything to the top
        left_panel.addStretch()

        # Create a widget for the left panel
        left_panel_widget = QWidget()
        left_panel_widget.setObjectName("left_panel_widget")
        left_panel_widget.setLayout(left_panel)

        # Use subtle background color instead of border, with reduced padding
        left_panel_widget.setStyleSheet("background-color: #f8f8f8; border-radius: 3px; padding: 8px;")

        middle_section.addWidget(left_panel_widget, 4)  # 40% of width - same as right panel

        # Center panel for join type
        center_panel = QVBoxLayout()
        center_panel.setSpacing(5)  # Reduce spacing between elements

        # Join type label
        join_type_label = QLabel("Join Type")
        join_type_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #444444;")
        center_panel.addWidget(join_type_label)

        # Add minimal spacing
        center_panel.addSpacing(5)

        # Join type list widget
        self.join_list = QListWidget()
        self.join_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #cccccc;
                padding: 2px;
            }
            QListWidget::item:selected {
                background-color: #e0e0ff;
                color: black;
            }
            QListWidget::item {
                padding: 3px;
            }
        """)
        self.join_list.setMaximumHeight(110)  # Limit height to make it more compact

        for join_type in ["inner", "outer", "left", "right"]:
            self.join_list.addItem(join_type)

        # Select inner join by default
        self.join_list.setCurrentRow(0)

        # Add descriptions for each join type
        join_descriptions = {
            "inner": "Returns only matching rows",
            "outer": "Returns all rows from both dataframes",
            "left": "Returns all rows from left dataframe",
            "right": "Returns all rows from right dataframe"
        }

        # Add tooltips to list items
        for i in range(self.join_list.count()):
            item = self.join_list.item(i)
            join_type = item.text()
            item.setToolTip(join_descriptions[join_type])

        center_panel.addWidget(self.join_list)

        # Add a visual representation of the join type
        join_diagram_label = QLabel("Join Diagram:")
        join_diagram_label.setStyleSheet("margin-top: 5px;")
        center_panel.addWidget(join_diagram_label)

        # Simple visual representation (can be improved with actual diagrams)
        self.join_diagram = QLabel("○ ⋂ ○")  # Default to inner join
        self.join_diagram.setStyleSheet("font-size: 20px; text-align: center; margin: 2px;")
        self.join_diagram.setAlignment(Qt.AlignmentFlag.AlignCenter)
        center_panel.addWidget(self.join_diagram)

        # Update diagram when join type changes
        self.join_list.currentRowChanged.connect(self.update_join_diagram)

        # Add stretch to push everything to the top
        center_panel.addStretch()

        # Create a widget for the center panel
        center_panel_widget = QWidget()
        center_panel_widget.setLayout(center_panel)

        # Use subtle background color instead of border, with reduced padding
        center_panel_widget.setStyleSheet("background-color: #f0f0f0; border-radius: 3px; padding: 8px;")

        middle_section.addWidget(center_panel_widget, 3)  # 30% of width

        # Right dataframe panel
        right_panel = QVBoxLayout()
        right_panel.setSpacing(5)  # Reduce spacing between elements

        # Right dataframe header with two rows
        right_header_layout = QVBoxLayout()
        right_header_layout.setSpacing(2)  # Reduce spacing between header elements

        right_label = QLabel("Right")
        right_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #444444;")
        right_header_layout.addWidget(right_label)

        # Create a grid layout for the dataframe selection
        df_selection_grid = QGridLayout()
        df_selection_grid.setSpacing(4)  # Reduce spacing

        right_header_layout.addLayout(df_selection_grid)
        right_panel.addLayout(right_header_layout)

        # Add minimal spacing
        right_panel.addSpacing(5)

        # Create a grid layout for the form fields
        right_grid_layout = QGridLayout()
        right_grid_layout.setSpacing(4)  # Reduce spacing in grid layout
        right_grid_layout.setContentsMargins(0, 0, 0, 0)  # Remove margins

        right_df_label = QLabel("Dataframe:")

        self.df_combo = QComboBox()
        self.df_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 1px solid #cccccc;
                padding: 2px;
            }
            QComboBox::drop-down {
                border: 0px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 10px;
                height: 10px;
            }
        """)
        self.df_combo.setMinimumWidth(150)  # Set minimum width for consistency

        for df_name in self.available_dataframes:
            if df_name != self.current_df_name:  # Exclude current dataframe
                self.df_combo.addItem(df_name)
        self.df_combo.currentTextChanged.connect(self.on_dataframe_changed)

        right_grid_layout.addWidget(right_df_label, 0, 0)
        right_grid_layout.addWidget(self.df_combo, 0, 1)
        # df_selection_grid.setColumnStretch(1, 1)

        # Join on columns
        join_on_label = QLabel("Join on columns:")
        self.right_on_edit = QLineEdit()
        self.right_on_edit.setStyleSheet("background-color: white; border: 1px solid #cccccc; padding: 2px;")
        self.right_on_edit.setMinimumWidth(150)  # Set minimum width for consistency
        right_grid_layout.addWidget(join_on_label, 1, 0)
        right_grid_layout.addWidget(self.right_on_edit, 1, 1)

        # Column suffix
        suffix_label = QLabel("Column suffix:")
        right_suffix_edit = QLineEdit("_y")
        right_suffix_edit.setStyleSheet("background-color: white; border: 1px solid #cccccc; padding: 2px;")
        right_suffix_edit.setMinimumWidth(150)  # Set minimum width for consistency
        right_grid_layout.addWidget(suffix_label, 2, 0)
        right_grid_layout.addWidget(right_suffix_edit, 2, 1)

        # Make the second column (fields) expand
        right_grid_layout.setColumnStretch(1, 1)

        right_panel.addLayout(right_grid_layout)

        # Column selection section
        column_selection_label = QLabel("Select columns to include:")
        column_selection_label.setStyleSheet("margin-top: 5px;")
        right_panel.addWidget(column_selection_label)

        # List widget for column selection with checkboxes
        self.columns_list = QListWidget()
        self.columns_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #cccccc;
                font-size: 14px;
                padding: 2px;
            }
            QListWidget::item:selected {
                background-color: white;
                font-size: 14px;
                color: black;
            }
            QListWidget::item {
                padding: 6px;
                margin-left: 6px;
                min-height: 24px;
            }
        """)
        self.columns_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        right_panel.addWidget(self.columns_list)

        # Select/Deselect All buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(4)  # Reduce spacing

        select_all_button = QPushButton("Select All")
        select_all_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 3px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        select_all_button.clicked.connect(self.select_all_columns)
        buttons_layout.addWidget(select_all_button)

        deselect_all_button = QPushButton("Deselect All")
        deselect_all_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 3px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        deselect_all_button.clicked.connect(self.deselect_all_columns)
        buttons_layout.addWidget(deselect_all_button)

        right_panel.addLayout(buttons_layout)

        # Create a widget for the right panel
        right_panel_widget = QWidget()
        right_panel_widget.setObjectName("right_panel_widget")
        right_panel_widget.setLayout(right_panel)

        # Use subtle background color instead of border, with reduced padding
        right_panel_widget.setStyleSheet("background-color: #f8f8f8; border-radius: 3px; padding: 8px;")

        middle_section.addWidget(right_panel_widget, 4)  # 40% of width - same as left panel

        main_layout.addLayout(middle_section)

        # Bottom section for additional options
        bottom_section = QVBoxLayout()
        bottom_section.setSpacing(5)  # Reduce spacing

        # Add a horizontal line separator
        line2 = QFrame()
        line2.setFrameShape(QFrame.Shape.HLine)
        line2.setFrameShadow(QFrame.Shadow.Sunken)
        line2.setMaximumHeight(1)  # Make the line thinner
        bottom_section.addWidget(line2)

        # Create a horizontal layout for the bottom panels
        bottom_panels_layout = QHBoxLayout()
        bottom_panels_layout.setSpacing(8)  # Reduce spacing between panels

        # Options panel
        options_panel = QVBoxLayout()
        options_panel.setSpacing(3)  # Reduce spacing

        options_label = QLabel("Options")
        options_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #444444;")
        options_panel.addWidget(options_label)

        # Indicator checkbox
        self.indicator_checkbox = QCheckBox("Add merge indicator column")
        self.indicator_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
                background-color: white;
                border: 2px solid #cccccc;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
            }
        """)
        self.indicator_checkbox.setToolTip("Adds a '_merge' column to indicate the source of each row")
        options_panel.addWidget(self.indicator_checkbox)

        # Create a widget for the options panel
        options_panel_widget = QWidget()
        options_panel_widget.setLayout(options_panel)
        options_panel_widget.setStyleSheet("background-color: #f5f5f5; border-radius: 3px; padding: 5px;")

        bottom_panels_layout.addWidget(options_panel_widget)

        # Result preview panel
        result_panel = QVBoxLayout()
        result_panel.setSpacing(3)  # Reduce spacing

        result_preview_label = QLabel("Result")
        result_preview_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #444444;")
        result_panel.addWidget(result_preview_label)

        # Result name preview
        result_name_layout = QHBoxLayout()
        result_name_layout.setSpacing(4)  # Reduce spacing

        result_name_label = QLabel("New dataframe name:")
        result_name_layout.addWidget(result_name_label)

        # This will be updated when dataframes are selected
        self.result_name_preview = QLabel()
        self.result_name_preview.setStyleSheet("font-weight: bold; color: #0066cc;")
        result_name_layout.addWidget(self.result_name_preview)

        # Add stretch to push everything to the left
        result_name_layout.addStretch()

        result_panel.addLayout(result_name_layout)

        # Create a widget for the result panel
        result_panel_widget = QWidget()
        result_panel_widget.setLayout(result_panel)
        result_panel_widget.setStyleSheet("background-color: #f5f5f5; border-radius: 3px; padding: 5px;")

        bottom_panels_layout.addWidget(result_panel_widget)

        bottom_section.addLayout(bottom_panels_layout)

        main_layout.addLayout(bottom_section)

        # Dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)

        # Style the buttons to make them more visible
        ok_button = button_box.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        cancel_button = button_box.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)

        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        # Add some spacing before the buttons
        main_layout.addSpacing(5)
        main_layout.addWidget(button_box)

        self.setLayout(main_layout)

        # Store reference to suffix edit fields for the accept method
        self.left_suffix_edit = left_suffix_edit
        self.right_suffix_edit = right_suffix_edit

        # Initialize column list if a dataframe is already selected
        if self.df_combo.currentText():
            self.update_right_columns(self.df_combo.currentText())

        # Update the result name preview
        self.update_result_name_preview()

    def on_dataframe_changed(self, df_name):
        """Handle dataframe selection change"""
        # Update columns
        self.update_right_columns(df_name)

        # Update result name preview
        self.update_result_name_preview()

    def update_result_name_preview(self):
        """Update the result name preview based on selected dataframes"""
        if not self.df_combo.currentText():
            self.result_name_preview.setText("")
            return

        # Generate the result name
        left_df_name = self.current_df_name
        right_df_name = self.df_combo.currentText()
        result_name = f"{left_df_name}_merged_with_{right_df_name}"

        self.result_name_preview.setText(result_name)

    def update_right_columns(self, df_name):
        """Update the list of columns from the selected right dataframe"""
        if not df_name:
            self.columns_list.clear()
            self.right_df_columns = []
            return

        # Get the dataframe
        right_df = self.parent.df_manager.get_dataframe(df_name)
        if right_df is None:
            return

        # Get columns
        self.right_df_columns = list(right_df.data.columns)

        # Update the list widget
        self.columns_list.clear()
        for column in self.right_df_columns:
            item = QListWidgetItem(column)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(Qt.CheckState.Checked)  # Default to checked
            self.columns_list.addItem(item)

    def select_all_columns(self):
        """Select all columns in the list"""
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            item.setCheckState(Qt.CheckState.Checked)

    def deselect_all_columns(self):
        """Deselect all columns in the list"""
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            item.setCheckState(Qt.CheckState.Unchecked)

    def update_join_diagram(self, row):
        """Update the join diagram based on the selected join type"""
        join_type = self.join_list.item(row).text()

        # Update the diagram based on join type
        if join_type == "inner":
            self.join_diagram.setText("○ ⋂ ○")  # Intersection symbol
        elif join_type == "outer":
            self.join_diagram.setText("○ ⋃ ○")  # Union symbol
        elif join_type == "left":
            self.join_diagram.setText("○ ⊃ ○")  # Left includes right
        elif join_type == "right":
            self.join_diagram.setText("○ ⊂ ○")  # Right includes left

    def accept(self):
        # Check if a dataframe is selected
        if self.df_combo.currentText() == "":
            QMessageBox.warning(self, "Warning", "Please select a dataframe to merge with.")
            return

        # Get join column values
        self.left_on = self.left_on_edit.text().strip()
        self.right_on = self.right_on_edit.text().strip()

        # Check if join columns are specified
        if not self.left_on:
            QMessageBox.warning(self, "Warning", "Please specify join columns for the left dataframe.")
            self.left_on_edit.setFocus()
            return

        if not self.right_on:
            QMessageBox.warning(self, "Warning", "Please specify join columns for the right dataframe.")
            self.right_on_edit.setFocus()
            return

        self.selected_dataframe = self.df_combo.currentText()

        # Get the selected join type from the list widget
        selected_items = self.join_list.selectedItems()
        if selected_items:
            self.how = selected_items[0].text()
        else:
            self.how = "inner"  # Default to inner join

        # Get suffixes from the individual suffix fields
        left_suffix = self.left_suffix_edit.text().strip()
        right_suffix = self.right_suffix_edit.text().strip()

        # Set the suffixes tuple
        if left_suffix and right_suffix:
            self.suffixes = (left_suffix, right_suffix)
        else:
            # Default suffixes if empty
            self.suffixes = ("_x", "_y")

        self.indicator = self.indicator_checkbox.isChecked()

        # Get selected columns
        self.selected_columns = []
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                self.selected_columns.append(item.text())

        super().accept()
