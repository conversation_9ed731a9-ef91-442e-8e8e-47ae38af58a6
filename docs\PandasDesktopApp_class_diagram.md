# PandasDesktopApp Class Diagram

```mermaid
classDiagram
    %% Main Application
    class PandasDesktopApp {
        -df_manager: PandasDataFrameManager
        -global_seq_no: int
        -file_manager: FileManager
        -df_viewer: DataFrameViewer
        -op_manager: OperationManager
        -code_manager: CodeManager
        -status_bar: QStatusBar
        +__init__()
        +get_next_sequence_number()
        +setup_ui()
        +undo_last_operation()
    }
    
    %% Components of the application
    class FileManager {
        -main_app: PandasDesktopApp
        +__init__(main_app)
        +load_csv()
    }
    
    class DataFrameViewer {
        -main_app: PandasDesktopApp
        +__init__(main_app)
        +add_dataframe_tab(df_name)
        +close_tab(index)
        +on_tab_changed()
    }
    
    class OperationManager {
        -main_app: PandasDesktopApp
        -copied_column
        -copied_column_name: str
        -saved_queries: List[str]
        +__init__(main_app)
        +load_saved_queries()
        +save_queries()
        +show_context_menu(position, df_name)
        +copy_column(df_name, column_index)
        +paste_column(df_name)
        +execute_query(df_name)
        +rename_column(df_name)
        +dt_date(df_name, column_index)
        +dt_month(df_name, column_index)
        +dt_day(df_name, column_index)
        +dt_hour(df_name, column_index)
        +dt_minute(df_name, column_index)
        +numpy_log(df_name, column_index)
        +numpy_exp(df_name, column_index)
        +numpy_sign(df_name, column_index)
        +arithmetic_operation(df_name, column_index, operation)
        +shift_column(df_name, column_index)
    }
    
    class CodeManager {
        -main_app: PandasDesktopApp
        -current_script: str
        -script_operations: Dict
        +__init__(main_app)
        +show_manager()
        +on_close_manager()
        +load_script()
        +save_script()
        +run_script()
        +generate_script(df_name)
        +export_script(df_name)
        +show_code(df_name)
        +copy_to_clipboard(text)
        +update_code(df_name)
        +generate_full_script()
        +refresh_dataframe_views()
    }
    
    %% Data management
    class PandasDataFrameManager {
        -dataframes: Dict[str, PandasDataFrame]
        -app: PandasDesktopApp
        -last_executed_script: str
        -_sequence_counter: int
        -_dataframe_operations: Dict
        +__init__(app)
        +get_dataframe(name)
        +get_all_dataframes()
        +add_dataframe(df, name, source_path)
        +remove_dataframe(name)
        +generate_full_script()
        +execute_script(script, interpreter, script_path)
    }
    
    %% Data models
    class PandasDataFrame {
        -data: pd.DataFrame
        -name: str
        -source_path: str
        -operations: List[Operation]
        -parent: str
        -hidden_columns: List
        +__init__(data, name, source_path)
        +add_operation(code, seq_no, operation_type, description, main_app)
        +generate_script()
        +rename_column(old_name, new_name, main_app)
        +add_column(name, values, source_column, main_app)
        +query(query_str)
    }
    
    class LazyLoadingPandasTableModel {
        -_data: pd.DataFrame
        -_cached_data: Dict
        -_row_count: int
        -_column_count: int
        -_chunk_size: int
        +data()
        +rowCount()
        +columnCount()
        +data(index, role)
        +headerData(section, orientation, role)
        +update_data(new_data)
    }
    
    class Operation {
        -code: str
        -seq_no: int
        -source_df: str
        -target_df: str
        -description: str
        -operation_type: str
    }
    
    %% UI Components
    class QueryDialog {
        -parent: OperationManager
        -saved_queries: List[str]
        -selected_queries: List[str]
        -query_text: str
        +__init__(parent, saved_queries)
        +setup_ui()
        +add_selected_to_editor()
        +delete_selected_queries()
        +save_current_query()
        +accept()
    }
    
    class MergeDialog {
        -parent: OperationManager
        -current_df_name: str
        -right_df_columns: List[str]
        +__init__(parent, df_name)
        +setup_ui()
        +on_dataframe_changed(df_name)
        +update_result_name_preview()
        +update_right_columns(df_name)
        +update_join_diagram(row)
    }
    
    class PythonInterpreter {
        -namespace: Dict
        -history: List
        -dataframes: Dict
        -dataframe_info: Dict
        -relationships: Dict
        -operations: Dict
        -current_sequence: List
        +__init__()
        +execute(code, script_path)
    }
    
    %% Inheritance
    QMainWindow <|-- PandasDesktopApp
    QTabWidget <|-- DataFrameViewer
    QDialog <|-- QueryDialog
    QDialog <|-- MergeDialog
    QAbstractTableModel <|-- LazyLoadingPandasTableModel
    
    %% Composition
    PandasDesktopApp *-- PandasDataFrameManager
    PandasDesktopApp *-- FileManager
    PandasDesktopApp *-- DataFrameViewer
    PandasDesktopApp *-- OperationManager
    PandasDesktopApp *-- CodeManager
    
    %% Associations
    PandasDataFrameManager "1" *-- "0..*" PandasDataFrame : manages
    FileManager .. PandasDataFrameManager : uses
    DataFrameViewer .. LazyLoadingPandasTableModel : displays
    PandasDataFrame "1" *-- "0..*" Operation : tracks
    OperationManager .. QueryDialog : uses
    OperationManager .. MergeDialog : uses
    CodeManager .. PythonInterpreter : uses
    DataFrameViewer -- OperationManager : interacts
```

## Class Relationships

1. **PandasDesktopApp** is the main application class that inherits from PyQt's QMainWindow
   - Contains references to all major components of the application
   - Now uses PandasDataFrameManager to manage dataframes instead of direct access

2. **PandasDataFrameManager** is a new central manager for all dataframe operations
   - Manages the lifecycle of dataframes
   - Provides methods for accessing, adding, and removing dataframes
   - Handles script execution and generation

3. **FileManager** handles file operations such as loading CSV files
   - Uses PandasDataFrameManager to create and store dataframes

4. **DataFrameViewer** manages the tabbed interface for viewing dataframes
   - Inherits from QTabWidget
   - Uses LazyLoadingPandasTableModel for efficient display of large dataframes

5. **OperationManager** provides functionality for manipulating dataframes
   - Implements operations like column manipulation, queries, and transformations
   - Uses QueryDialog for complex query operations
   - Uses MergeDialog for dataframe merge operations

6. **CodeManager** handles code generation and script management
   - Generates Python code representing the operations performed
   - Manages loading, saving, and running scripts
   - Uses PythonInterpreter for script execution

7. **PandasDataFrame** is a wrapper around pandas DataFrame
   - Maintains a history of operations performed
   - Each operation is tracked as an Operation object
   - Now includes support for hidden columns

8. **LazyLoadingPandasTableModel** replaces the previous PandasTableModel
   - Provides efficient loading of large dataframes by loading data in chunks
   - Inherits from QAbstractTableModel

9. **PythonInterpreter** is a new class for executing Python code
   - Provides a controlled environment for script execution
   - Tracks dataframes and their relationships during execution

10. **MergeDialog** is a new UI component for merging dataframes
    - Provides visual representation of join types
    - Allows selection of columns to include in the merge
