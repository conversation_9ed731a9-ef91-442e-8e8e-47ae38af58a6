import pandas as pd
from PyQt6.QtWidgets import QFileDialog
from pandas_dataframe import PandasDataFrame

class FileManager:
    def __init__(self, main_app):
        self.main_app = main_app
        
    def load_csv(self):
        file_paths, _ = QFileDialog.getOpenFileNames(
            self.main_app, "Select CSV Files", "", "CSV Files (*.csv)")
        
        for path in file_paths:
            # Extract name from the file path
            df_name = 'df_' + path.split("/")[-1].split("\\")[-1].replace(".csv", "")
            
            # Use the DataFrame Manager to load the CSV
            loaded_df_name = self.main_app.df_manager.load_csv(path, df_name)
            
            if loaded_df_name:
                # Display in viewer
                self.main_app.df_viewer.add_dataframe_tab(loaded_df_name)
                self.main_app.status_bar.showMessage(f"Loaded {loaded_df_name} from {path}")
            else:
                self.main_app.status_bar.showMessage(f"Failed to load {path}")

