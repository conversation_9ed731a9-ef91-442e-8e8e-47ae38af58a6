from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSpinBox, QPushButton, QFormLayout, QLineEdit)
from PyQt6.QtCore import Qt

class RollingOperationDialog(QDialog):
    """
    Dialog for configuring a rolling window operation on a dataframe column.
    """
    def __init__(self, parent, column_name, operation):
        """
        Initialize the rolling operation dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            column_name: The name of the column to apply the rolling operation to
            operation: The rolling operation to apply (e.g., 'max', 'min', 'mean', 'median')
        """
        super().__init__(parent)
        self.parent = parent
        self.column_name = column_name
        self.operation = operation
        self.window_size = 2
        self.result_column_name = f"{column_name}_rolling_2_{operation}"
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle(f"Rolling {self.operation.capitalize()}: {self.column_name}")
        self.setMinimumWidth(450)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Column name display with consistent styling
        column_label = QLabel("Column:")
        column_value = QLabel(self.column_name)
        column_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(column_label, column_value)

        # Operation display
        operation_label = QLabel("Operation:")
        operation_value = QLabel(self.operation.capitalize())
        operation_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(operation_label, operation_value)

        # Window size input
        self.window_spin = QSpinBox()
        self.window_spin.setRange(1, 1000)
        self.window_spin.setValue(2)
        self.window_spin.setSingleStep(1)
        form_layout.addRow("Window size:", self.window_spin)

        # Result column name preview
        self.result_name_edit = QLineEdit(self.result_column_name)
        form_layout.addRow("Result column name:", self.result_name_edit)

        # Connect window size spin box to update result name
        def update_result_name():
            window_size = self.window_spin.value()
            self.result_name_edit.setText(f"{self.column_name}_rolling_{window_size}_{self.operation}")
            
        self.window_spin.valueChanged.connect(update_result_name)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add description
        description = QLabel(f"Calculates the {self.operation} over a rolling window of specified size.")
        description.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(description)

        # Add some spacing
        main_layout.addSpacing(15)

        # Add buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Connect buttons
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.accept)
        
    def accept(self):
        """Handle dialog acceptance and store the values."""
        self.window_size = self.window_spin.value()
        self.result_column_name = self.result_name_edit.text()
        super().accept()
