import sys
import os
import unittest
import pandas as pd
import numpy as np
import ast
from unittest.mock import patch, MagicMock

# Add the parent directory to sys.path to import modules from the main project
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from python_interpreter import PythonInterpreter
from operation import Operation


class TestPythonInterpreter(unittest.TestCase):
    """Test cases for the PythonInterpreter class."""

    def setUp(self):
        """Set up test fixtures before each test method."""
        self.interpreter = PythonInterpreter()
        
        # Create a mock app for testing
        self.mock_app = MagicMock()
        self.mock_app.get_next_sequence_number.return_value = 100
    
    def tearDown(self):
        """Clean up after each test method."""
        self.interpreter = None
    
    def test_init(self):
        """Test the initialization of PythonInterpreter."""
        # Check that the namespace is initialized with required libraries
        self.assertIn('pd', self.interpreter.namespace)
        self.assertIn('np', self.interpreter.namespace)
        self.assertIn('__builtins__', self.interpreter.namespace)
        self.assertIn('__file__', self.interpreter.namespace)
        
        # Check that the history and dataframes are empty
        self.assertEqual(len(self.interpreter.history), 0)
        self.assertEqual(len(self.interpreter.dataframes), 0)
        self.assertEqual(len(self.interpreter.dataframe_info), 0)
        self.assertEqual(len(self.interpreter.relationships), 0)
        self.assertEqual(len(self.interpreter.operations), 0)
        self.assertEqual(len(self.interpreter.current_sequence), 0)
    
    def test_reset(self):
        """Test the reset method."""
        # Add some data to the interpreter
        self.interpreter.history.append("df = pd.DataFrame({'a': [1, 2, 3]})")
        self.interpreter.dataframes['df'] = pd.DataFrame({'a': [1, 2, 3]})
        self.interpreter.dataframe_info['df'] = {'source_path': 'test'}
        self.interpreter.relationships['df2'] = 'df'
        self.interpreter.operations['df'] = [{'type': 'create'}]
        
        # Reset the interpreter
        self.interpreter.reset()
        
        # Check that everything is reset
        self.assertEqual(len(self.interpreter.history), 0)
        self.assertEqual(len(self.interpreter.dataframes), 0)
        self.assertEqual(len(self.interpreter.dataframe_info), 0)
        self.assertEqual(len(self.interpreter.relationships), 0)
        self.assertEqual(len(self.interpreter.operations), 0)
    
    def test_execute_simple_code(self):
        """Test executing simple code that creates a dataframe."""
        code = "df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})"
        result = self.interpreter.execute(code)
        
        # Check that the execution was successful
        self.assertTrue(result['success'])
        
        # Check that the dataframe was created
        self.assertIn('df', result['dataframes'])
        self.assertEqual(result['dataframes']['df'].shape, (3, 2))
        
        # Check that the dataframe is tracked in the interpreter
        self.assertIn('df', self.interpreter.dataframes)
        self.assertEqual(self.interpreter.dataframes['df'].shape, (3, 2))
        
        # Check that the code is in the history
        self.assertEqual(len(self.interpreter.history), 1)
        self.assertEqual(self.interpreter.history[0], code)
    
    def test_execute_script_merge1(self):
        """Test executing simple code that creates a dataframe."""
        code_lines = [
            "df1 = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})",
            "df2 = pd.DataFrame({'a': [1, 2, 3], 'b': [7, 8, 9]})",
            "df_merge = df1.merge(df2, how='left', on='a')"
        ]
        result = self.interpreter.execute('\n'.join(code_lines))
        
        # Check that the execution was successful
        self.assertTrue(result['success'])
        
        # Check that the dataframe was created
        self.assertIn('df_merge', result['dataframes'])
        df_merge = result['dataframes']['df_merge']
        print(df_merge)
        
    def test_execute_with_error(self):
        """Test executing code with an error."""
        code = "df = pd.DataFrame({'a': [1, 2, 3]})\n1/0"  # Division by zero error
        result = self.interpreter.execute(code)
        
        # Check that the execution failed
        self.assertFalse(result['success'])
        
        # Check that error information is provided
        self.assertIn('error', result)
        self.assertEqual(result['error']['type'], 'ZeroDivisionError')
        
        # Check that the code is still in the history
        self.assertEqual(len(self.interpreter.history), 1)
        self.assertEqual(self.interpreter.history[0], code)
    
    def test_analyze_code_dataframe_creation(self):
        """Test analyzing code that creates a dataframe."""
        code = "df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})"
        analysis = self.interpreter.analyze_code(code)
        
        # Check that the analysis contains dataframe_assigns
        self.assertIn('dataframe_assigns', analysis)
        
        # No operations should be detected for simple dataframe creation
        self.assertIn('dataframe_ops', analysis)
        self.assertEqual(len(analysis['dataframe_ops']), 0)
    
    def test_analyze_code_csv_import(self):
        """Test analyzing code that imports a dataframe from CSV."""
        code = "df = pd.read_csv('data.csv')"
        analysis = self.interpreter.analyze_code(code)
        
        # Check that the analysis contains dataframe_assigns with csv_import type
        self.assertIn('dataframe_assigns', analysis)
        self.assertIn('df', analysis['dataframe_assigns'])
        self.assertEqual(analysis['dataframe_assigns']['df']['type'], 'csv_import')
        self.assertEqual(analysis['dataframe_assigns']['df']['source'], 'data.csv')
    
    def test_analyze_code_query(self):
        """Test analyzing code with a query operation."""
        code = "df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})\ndf2 = df.query('a > 1')"
        analysis = self.interpreter.analyze_code(code)
        
        # Check that the analysis contains parent-child relationship
        self.assertIn('parent_child', analysis)
        self.assertIn('df2', analysis['parent_child'])
        self.assertEqual(analysis['parent_child']['df2'], 'df')
        
        # Check that the query operation is detected
        self.assertIn('dataframe_ops', analysis)
        self.assertIn('df', analysis['dataframe_ops'])
        
        # Find the query operation
        query_ops = [op for op in analysis['dataframe_ops'].get('df', []) if op['type'] == 'query']
        self.assertGreaterEqual(len(query_ops), 1)
        self.assertEqual(query_ops[0]['query'], 'a > 1')
    
    def test_analyze_code_column_add(self):
        """Test analyzing code that adds a column to a dataframe."""
        code = "df = pd.DataFrame({'a': [1, 2, 3]})\ndf['b'] = df['a'] * 2"
        
        # Execute the code first to create the dataframe
        self.interpreter.execute(code)
        
        # Then analyze the code
        analysis = self.interpreter.analyze_code(code)
        
        # Check line operations for column addition
        self.assertIn('line_operations', analysis)
        
        # Find column_add operations
        column_ops = [op for op in analysis['line_operations'] if op['type'] == 'column_add']
        self.assertGreaterEqual(len(column_ops), 1)
        self.assertEqual(column_ops[0]['column'], 'b')
    
    def test_find_dataframes(self):
        """Test finding dataframes in the namespace."""
        # Create a dataframe in the namespace
        self.interpreter.namespace['df'] = pd.DataFrame({'a': [1, 2, 3]})
        
        # Record initial variables
        initial_vars = set(self.interpreter.namespace.keys())
        
        # Add a new dataframe
        self.interpreter.namespace['df2'] = pd.DataFrame({'b': [4, 5, 6]})
        
        # Find new dataframes
        found = self.interpreter._find_dataframes(initial_vars)
        
        # Check that df2 is found but df is not (since it was in initial_vars)
        self.assertIn('df2', found)
        self.assertNotIn('df', found)
        
        # Check that df2 is added to tracked dataframes
        self.assertIn('df2', self.interpreter.dataframes)
        self.assertIn('df2', self.interpreter.dataframe_info)
    
    def test_update_relationships(self):
        """Test updating dataframe relationships."""
        # Create dataframes
        df1 = pd.DataFrame({'a': [1, 2, 3]})
        df2 = pd.DataFrame({'a': [2, 3]})
        
        # Add to interpreter
        self.interpreter.dataframes['df1'] = df1
        
        # Create analysis result with parent-child relationship
        analysis = {'parent_child': {'df2': 'df1'}}
        
        # Update relationships
        self.interpreter._update_relationships(analysis, {'df2': df2})
        
        # Check that the relationship is updated
        self.assertIn('df2', self.interpreter.relationships)
        self.assertEqual(self.interpreter.relationships['df2'], 'df1')
    
    def test_generate_operations(self):
        """Test generating operation records."""
        # Create dataframes
        df1 = pd.DataFrame({'a': [1, 2, 3]})
        df2 = pd.DataFrame({'a': [2, 3]})
        
        # Add to interpreter
        self.interpreter.dataframes['df1'] = df1
        self.interpreter.dataframes['df2'] = df2
        
        # Create analysis result with operations
        analysis = {
            'dataframe_ops': {
                'df1': [
                    {
                        'type': 'query',
                        'parent': 'df1',
                        'query': 'a > 1',
                        'code': "df2 = df1.query('a > 1')",
                        'node': MagicMock(lineno=2)
                    }
                ]
            }
        }
        
        # Generate operations
        self.interpreter._generate_operations(analysis, {'df2': df2})
        
        # Check that operations are generated
        self.assertIn('df1', self.interpreter.operations)
        self.assertEqual(len(self.interpreter.operations['df1']), 1)
        self.assertEqual(self.interpreter.operations['df1'][0]['type'], 'query')
    
    def test_get_error_line(self):
        """Test getting the line number where an error occurred."""
        # Create an exception with lineno attribute
        error = SyntaxError('invalid syntax')
        error.lineno = 5
        
        # Get error line
        line = self.interpreter._get_error_line(error, "code")
        
        # Check that the line number is correct
        self.assertEqual(line, 5)
    
    def test_get_dataframe(self):
        """Test getting a dataframe by name."""
        # Create a dataframe
        df = pd.DataFrame({'a': [1, 2, 3]})
        
        # Add to interpreter
        self.interpreter.dataframes['df'] = df
        
        # Get the dataframe
        result = self.interpreter.get_dataframe('df')
        
        # Check that the dataframe is returned
        self.assertIs(result, df)
        
        # Check that a non-existent dataframe returns None
        self.assertIsNone(self.interpreter.get_dataframe('nonexistent'))
    
    def test_get_all_dataframes(self):
        """Test getting all tracked dataframes."""
        # Create dataframes
        df1 = pd.DataFrame({'a': [1, 2, 3]})
        df2 = pd.DataFrame({'b': [4, 5, 6]})
        
        # Add to interpreter
        self.interpreter.dataframes['df1'] = df1
        self.interpreter.dataframes['df2'] = df2
        
        # Get all dataframes
        result = self.interpreter.get_all_dataframes()
        
        # Check that all dataframes are returned
        self.assertEqual(len(result), 2)
        self.assertIn('df1', result)
        self.assertIn('df2', result)
        self.assertIs(result['df1'], df1)
        self.assertIs(result['df2'], df2)
    
    def test_get_relationships(self):
        """Test getting dataframe relationships."""
        # Add relationships
        self.interpreter.relationships['df2'] = 'df1'
        self.interpreter.relationships['df3'] = 'df2'
        
        # Get relationships
        result = self.interpreter.get_relationships()
        
        # Check that relationships are returned
        self.assertEqual(len(result), 2)
        self.assertEqual(result['df2'], 'df1')
        self.assertEqual(result['df3'], 'df2')
    
    def test_get_operations(self):
        """Test getting operations for dataframes."""
        # Add operations
        self.interpreter.operations['df1'] = [{'type': 'create'}]
        self.interpreter.operations['df2'] = [{'type': 'query'}, {'type': 'column_add'}]
        
        # Get all operations
        all_ops = self.interpreter.get_operations()
        
        # Check that all operations are returned
        self.assertEqual(len(all_ops), 2)
        self.assertEqual(len(all_ops['df1']), 1)
        self.assertEqual(len(all_ops['df2']), 2)
        
        # Get operations for a specific dataframe
        df2_ops = self.interpreter.get_operations('df2')
        
        # Check that only df2 operations are returned
        self.assertEqual(len(df2_ops), 1)
        self.assertIn('df2', df2_ops)
        self.assertEqual(len(df2_ops['df2']), 2)
    
    def test_get_execution_history(self):
        """Test getting the history of executed code."""
        # Add code to history
        self.interpreter.history.append("code1")
        self.interpreter.history.append("code2")
        
        # Get history
        history = self.interpreter.get_execution_history()
        
        # Check that history is returned
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0], "code1")
        self.assertEqual(history[1], "code2")
    
    def test_create_operation_objects(self):
        """Test converting internal operation records to Operation objects."""
        # Add operations
        self.interpreter.operations['df1'] = [
            {
                'code': "df1 = pd.DataFrame({'a': [1, 2, 3]})",
                'type': 'create',
                'source_df': 'df1',
                'target_df': 'df1',
                'description': 'Create dataframe'
            }
        ]
        
        # Convert to Operation objects
        result = self.interpreter.create_operation_objects(self.mock_app)
        
        # Check that Operation objects are created
        self.assertIn('df1', result)
        self.assertEqual(len(result['df1']), 1)
        self.assertIsInstance(result['df1'][0], Operation)
        self.assertEqual(result['df1'][0].seq_no, 100)
        self.assertEqual(result['df1'][0].operation_type, 'create')
    
    def test_convert_to_pandas_dataframes(self):
        """Test converting raw pandas dataframes to PandasDataFrame objects."""
        # Create a mock PandasDataFrame class
        mock_pandas_df = MagicMock()
        mock_pandas_df.operations = []
        
        # Create a mock PandasDataFrame constructor
        mock_pandas_df_class = MagicMock(return_value=mock_pandas_df)
        
        # Add dataframes and relationships
        self.interpreter.dataframes['df1'] = pd.DataFrame({'a': [1, 2, 3]})
        self.interpreter.dataframe_info['df1'] = {'source_path': 'test.csv'}
        self.interpreter.relationships['df2'] = 'df1'
        
        # Add operations
        self.interpreter.operations['df1'] = [
            {
                'code': "df1 = pd.DataFrame({'a': [1, 2, 3]})",
                'type': 'create',
                'source_df': 'df1',
                'target_df': 'df1',
                'description': 'Create dataframe'
            }
        ]
        
        # Patch the import inside the method
        with patch.dict('sys.modules', {'pandas_dataframe': MagicMock()}):
            # Set the PandasDataFrame attribute on the mock module
            sys.modules['pandas_dataframe'].PandasDataFrame = mock_pandas_df_class
            
            # Convert to PandasDataFrame objects
            result = self.interpreter.convert_to_pandas_dataframes(self.mock_app)
        
        # Check that PandasDataFrame objects are created
        self.assertIn('df1', result)
        self.assertIs(result['df1'], mock_pandas_df)
        
        # Check that PandasDataFrame was created with correct arguments
        mock_pandas_df_class.assert_called_once()
        args, kwargs = mock_pandas_df_class.call_args
        self.assertEqual(kwargs['name'], 'df1')
        self.assertEqual(kwargs['source_path'], 'test.csv')
        
        # Check that operations were added to the PandasDataFrame
        self.assertEqual(len(mock_pandas_df.operations), 1)


if __name__ == '__main__':
    unittest.main()
