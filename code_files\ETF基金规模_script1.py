import pandas as pd
import numpy as np

# DataFrame Operations
# Import CSV from etf_fund_data_merged.csv (#1)
df_df_etf_fund_data_merged = pd.read_csv('E:/git/desktop_pandas/data_files/etf_fund_data_merged.csv')

# Import CSV from etf_fund_data_merged.csv (#1)
df_etf_fund_data_merged = pd.read_csv('E:/git/desktop_pandas/data_files/etf_fund_data_merged.csv')

# Extract date from '统计日期' (#2)
df_etf_fund_data_merged['统计日期_date'] = pd.to_datetime(df_etf_fund_data_merged['统计日期']).dt.date

# Create line chart of '统计日期_date' vs '总规模（亿元）' (#3)
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

# Configure fonts to support Chinese characters
if platform.system() == 'Windows':
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
elif platform.system() == 'Darwin':  # macOS
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti', 'Heiti TC', 'Arial Unicode MS', 'sans-serif']
else:  # Linux and others
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Droid Sans Fallback', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False  # Ensure minus sign is displayed correctly

plt.figure(figsize=(10, 6))
plt.plot(df_etf_fund_data_merged['统计日期_date'], df_etf_fund_data_merged['总规模（亿元）'], label='总规模（亿元）')
plt.title('df_etf_fund_data_merged - line chart')
plt.xlabel('统计日期_date')
plt.ylabel('Values')
plt.legend()
plt.grid(True)
plt.show()
