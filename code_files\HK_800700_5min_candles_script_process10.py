import pandas as pd
import numpy as np
from pathlib import Path

# Data Loading
df_HK_800700_5min_candles = pd.read_csv(Path(__file__).parent.parent / 'data_files/HK_800700_5min_candles.csv')

# DataFrame Operations
# Add/modify column: time_date (#9)
df_HK_800700_5min_candles['time_date'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.date

# Add/modify column: time_hour (#10)
df_HK_800700_5min_candles['time_hour'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.hour

# Add/modify column: time_minute (#11)
df_HK_800700_5min_candles['time_minute'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.minute

# Add/modify column: time_hour_*60.0 (#12)
df_HK_800700_5min_candles['time_hour_*60.0'] = df_HK_800700_5min_candles['time_hour'] * 60.0

# Add/modify column: time_hour_*60.0_+_time_minute (#13)
df_HK_800700_5min_candles['time_hour_*60.0_+_time_minute'] = df_HK_800700_5min_candles['time_hour_*60.0'] + df_HK_800700_5min_candles['time_minute']

# Operation: rename (#14)
df_HK_800700_5min_candles.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)

# Add/modify column: open_groupby_time_date (#15)
df_HK_800700_5min_candles['open_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['open'].transform('first')

# Operation: rename (#16)
df_HK_800700_5min_candles.rename(columns={'open_groupby_time_date': 'day_open'}, inplace=True)

# Add/modify column: close_groupby_time_date (#17)
df_HK_800700_5min_candles['close_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['close'].transform('last')

# Operation: rename (#18)
df_HK_800700_5min_candles.rename(columns={'close_groupby_time_date': 'day_close'}, inplace=True)

# Add/modify column: close_rolling_6_mean (#19)
df_HK_800700_5min_candles['close_rolling_6_mean'] = df_HK_800700_5min_candles['close'].rolling(window=6).mean()

# Operation: rename (#20)
df_HK_800700_5min_candles.rename(columns={'close_rolling_6_mean': 'close_MA6'}, inplace=True)

# Add/modify column: close_rolling_12_mean (#21)
df_HK_800700_5min_candles['close_rolling_12_mean'] = df_HK_800700_5min_candles['close'].rolling(window=12).mean()

# Operation: rename (#22)
df_HK_800700_5min_candles.rename(columns={'close_rolling_12_mean': 'close_MA12'}, inplace=True)

# Add/modify column: close_rolling_24_mean (#23)
df_HK_800700_5min_candles['close_rolling_24_mean'] = df_HK_800700_5min_candles['close'].rolling(window=24).mean()

# Operation: rename (#24)
df_HK_800700_5min_candles.rename(columns={'close_rolling_24_mean': 'close_MA24'}, inplace=True)

# Add/modify column: close_rolling_48_mean (#25)
df_HK_800700_5min_candles['close_rolling_48_mean'] = df_HK_800700_5min_candles['close'].rolling(window=48).mean()

# Operation: rename (#26)
df_HK_800700_5min_candles.rename(columns={'close_rolling_48_mean': 'close_MA48'}, inplace=True)

# Add/modify column: close_shift1 (#27)
df_HK_800700_5min_candles['close_shift1'] = df_HK_800700_5min_candles['close'].shift(1)

# Add/modify column: close_shift1_groupby_time_date (#28)
df_HK_800700_5min_candles['close_shift1_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['close_shift1'].transform('first')

# Operation: rename (#29)
df_HK_800700_5min_candles.rename(columns={'close_shift1_groupby_time_date': 'pre_day_close'}, inplace=True)

# Add/modify column: high_groupby_time_date (#30)
df_HK_800700_5min_candles['high_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['high'].transform(lambda x: x.expanding().max())

# Operation: rename (#31)
df_HK_800700_5min_candles.rename(columns={'high_groupby_time_date': 'day_high_till_now'}, inplace=True)

# Add/modify column: low_groupby_time_date (#32)
df_HK_800700_5min_candles['low_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['low'].transform(lambda x: x.expanding().min())

# Operation: rename (#33)
df_HK_800700_5min_candles.rename(columns={'low_groupby_time_date': 'day_low_till_now'}, inplace=True)

# Add/modify column: close_groupby_time_date (#34)
df_HK_800700_5min_candles['close_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['close'].transform(lambda x: x.expanding().max())

# Operation: rename (#35)
df_HK_800700_5min_candles.rename(columns={'close_groupby_time_date': 'day_5min_close_high_till_now'}, inplace=True)

# Add/modify column: close_groupby_time_date (#36)
df_HK_800700_5min_candles['close_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['close'].transform(lambda x: x.expanding().min())

# Operation: rename (#37)
df_HK_800700_5min_candles.rename(columns={'close_groupby_time_date': 'day_5min_close_low_till_now'}, inplace=True)

# Add/modify column: open_log (#38)
df_HK_800700_5min_candles['open_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['open'], errors='coerce'))

# Add/modify column: high_log (#39)
df_HK_800700_5min_candles['high_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['high'], errors='coerce'))

# Add/modify column: low_log (#40)
df_HK_800700_5min_candles['low_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['low'], errors='coerce'))

# Add/modify column: close_log (#41)
df_HK_800700_5min_candles['close_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['close'], errors='coerce'))

# Add/modify column: day_open_log (#42)
df_HK_800700_5min_candles['day_open_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['day_open'], errors='coerce'))

# Add/modify column: day_close_log (#43)
df_HK_800700_5min_candles['day_close_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['day_close'], errors='coerce'))

# Add/modify column: close_MA6_log (#44)
df_HK_800700_5min_candles['close_MA6_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['close_MA6'], errors='coerce'))

# Add/modify column: close_MA12_log (#45)
df_HK_800700_5min_candles['close_MA12_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['close_MA12'], errors='coerce'))

# Add/modify column: close_MA24_log (#46)
df_HK_800700_5min_candles['close_MA24_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['close_MA24'], errors='coerce'))

# Add/modify column: close_MA48_log (#47)
df_HK_800700_5min_candles['close_MA48_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['close_MA48'], errors='coerce'))

# Add/modify column: close_shift1_log (#48)
df_HK_800700_5min_candles['close_shift1_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['close_shift1'], errors='coerce'))

# Add/modify column: pre_day_close_log (#49)
df_HK_800700_5min_candles['pre_day_close_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['pre_day_close'], errors='coerce'))

# Add/modify column: day_high_till_now_log (#50)
df_HK_800700_5min_candles['day_high_till_now_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['day_high_till_now'], errors='coerce'))

# Add/modify column: day_low_till_now_log (#51)
df_HK_800700_5min_candles['day_low_till_now_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['day_low_till_now'], errors='coerce'))

# Add/modify column: day_5min_close_high_till_now_log (#52)
df_HK_800700_5min_candles['day_5min_close_high_till_now_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['day_5min_close_high_till_now'], errors='coerce'))

# Add/modify column: day_5min_close_low_till_now_log (#53)
df_HK_800700_5min_candles['day_5min_close_low_till_now_log'] = np.log(pd.to_numeric(df_HK_800700_5min_candles['day_5min_close_low_till_now'], errors='coerce'))

# Add/modify column: close_log_-_day_open_log (#54)
df_HK_800700_5min_candles['close_log_-_day_open_log'] = df_HK_800700_5min_candles['close_log'] - df_HK_800700_5min_candles['day_open_log']

# Add/modify column: close_log_-_close_MA6_log (#55)
df_HK_800700_5min_candles['close_log_-_close_MA6_log'] = df_HK_800700_5min_candles['close_log'] - df_HK_800700_5min_candles['close_MA6_log']

# Add/modify column: close_log_-_close_MA12_log (#56)
df_HK_800700_5min_candles['close_log_-_close_MA12_log'] = df_HK_800700_5min_candles['close_log'] - df_HK_800700_5min_candles['close_MA12_log']

# Add/modify column: close_log_-_close_MA24_log (#57)
df_HK_800700_5min_candles['close_log_-_close_MA24_log'] = df_HK_800700_5min_candles['close_log'] - df_HK_800700_5min_candles['close_MA24_log']

# Add/modify column: close_log_-_close_MA48_log (#58)
df_HK_800700_5min_candles['close_log_-_close_MA48_log'] = df_HK_800700_5min_candles['close_log'] - df_HK_800700_5min_candles['close_MA48_log']

# Add/modify column: close_log_diff1 (#59)
df_HK_800700_5min_candles['close_log_diff1'] = df_HK_800700_5min_candles['close_log'].diff(1)

# Add/modify column: close_log_diff2 (#60)
df_HK_800700_5min_candles['close_log_diff2'] = df_HK_800700_5min_candles['close_log'].diff(2)

# Add/modify column: close_log_diff3 (#61)
df_HK_800700_5min_candles['close_log_diff3'] = df_HK_800700_5min_candles['close_log'].diff(3)

# Add/modify column: close_MA6_log_diff2 (#62)
df_HK_800700_5min_candles['close_MA6_log_diff2'] = df_HK_800700_5min_candles['close_MA6_log'].diff(2)

# Add/modify column: close_MA12_log_diff2 (#63)
df_HK_800700_5min_candles['close_MA12_log_diff2'] = df_HK_800700_5min_candles['close_MA12_log'].diff(2)

# Add/modify column: close_MA24_log_diff2 (#64)
df_HK_800700_5min_candles['close_MA24_log_diff2'] = df_HK_800700_5min_candles['close_MA24_log'].diff(2)

# Add/modify column: close_MA48_log_diff2 (#65)
df_HK_800700_5min_candles['close_MA48_log_diff2'] = df_HK_800700_5min_candles['close_MA48_log'].diff(2)

# Add/modify column: close_log_-_pre_day_close_log (#66)
df_HK_800700_5min_candles['close_log_-_pre_day_close_log'] = df_HK_800700_5min_candles['close_log'] - df_HK_800700_5min_candles['pre_day_close_log']

# Perform - operation on 'day_open_log' (#67)
df_HK_800700_5min_candles['day_open_log_-_pre_day_close_log'] = df_HK_800700_5min_candles['day_open_log'] - df_HK_800700_5min_candles['pre_day_close_log']
