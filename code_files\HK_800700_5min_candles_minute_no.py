import pandas as pd
import numpy as np
from pathlib import Path

# DataFrame Operations
# Import CSV from HK_800700_5min_candles.csv (#1)
df_HK_800700_5min_candles = pd.read_csv(Path(__file__).parent.parent / 'data_files/HK_800700_5min_candles.csv')

# Extract date from 'time' (#2)
df_HK_800700_5min_candles['time_date'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.date

# Extract hour from 'time' (#3)
df_HK_800700_5min_candles['time_hour'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.hour

# Extract minute from 'time' (#4)
df_HK_800700_5min_candles['time_minute'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.minute

# Perform * operation on 'time_hour' (#5)
df_HK_800700_5min_candles['time_hour_*60.0'] = df_HK_800700_5min_candles['time_hour'] * 60.0

# Perform + operation on 'time_hour_*60.0' (#6)
df_HK_800700_5min_candles['time_hour_*60.0_+_time_minute'] = df_HK_800700_5min_candles['time_hour_*60.0'] + df_HK_800700_5min_candles['time_minute']

# Renamed column 'time_hour_*60.0_+_time_minute' to 'minute_no' (#7)
df_HK_800700_5min_candles.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)

# Drop columns: 'time_hour_*60.0' (#8)
df_HK_800700_5min_candles = df_HK_800700_5min_candles.drop(columns=['time_hour_*60.0'])
print(df_HK_800700_5min_candles)
