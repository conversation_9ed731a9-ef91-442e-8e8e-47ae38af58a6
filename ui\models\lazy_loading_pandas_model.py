from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, pyqtSignal, QTimer
from PyQt6.QtWidgets import QApplication
import pandas as pd
import numpy as np

class LazyLoadingPandasTableModel(QAbstractTableModel):
    """
    A table model for pandas DataFrames that implements lazy loading.
    Only displays a limited number of rows at a time, loading more as needed.
    """
    # Signal emitted when more data is loaded
    dataLoaded = pyqtSignal(int, int)  # start_row, end_row

    def __init__(self, dataframe, pandas_df=None, chunk_size=500):
        """
        Initialize the model with a pandas DataFrame.

        Args:
            dataframe (pd.DataFrame): The pandas DataFrame to display
            pandas_df (PandasDataFrame, optional): The PandasDataFrame wrapper that contains this dataframe
            chunk_size (int): Number of rows to display at a time (default: 500)
        """
        super().__init__()
        self._full_data = dataframe
        self._chunk_size = chunk_size
        self._pandas_df = pandas_df  # Store reference to the PandasDataFrame wrapper

        # Always start with exactly chunk_size rows (or less if the dataframe is smaller)
        self._visible_rows = min(chunk_size, len(dataframe))
        self._allow_auto_fetch = False  # Flag to control automatic fetching
        self._data_initialized = False  # Flag to track if data has been initialized

        print(f"LazyLoadingPandasTableModel initialized with {len(dataframe)} rows, showing {self._visible_rows}")

        # Emit signal for initial load
        self.dataLoaded.emit(0, self._visible_rows)

    def rowCount(self, parent=QModelIndex()):
        """Return the number of rows currently visible in the model."""
        if parent.isValid():
            return 0
        return self._visible_rows

    def columnCount(self, parent=QModelIndex()):
        """Return the number of visible columns in the dataframe."""
        if parent.isValid():
            return 0
        # Return only the count of visible columns (excluding hidden ones)
        if self._pandas_df:
            return len(self._full_data.columns) - len(self._pandas_df.hidden_columns)
        return len(self._full_data.columns)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        """Return the data at the given index."""
        if not index.isValid():
            return None

        if role == Qt.ItemDataRole.DisplayRole:
            row = index.row()
            model_col = index.column()

            # Map the model column index to the actual dataframe column index
            # by accounting for hidden columns
            actual_col = self._map_to_actual_column(model_col)

            # Make sure we're within bounds
            if row < 0 or row >= len(self._full_data) or actual_col < 0 or actual_col >= len(self._full_data.columns):
                return None

            # Get the value from the dataframe
            value = self._full_data.iloc[row, actual_col]

            # Convert numpy types to Python types for better display
            if isinstance(value, (np.integer, np.floating, np.bool_)):
                return str(value)
            elif pd.isna(value):
                return ""
            else:
                return str(value)

        return None

    def _map_to_actual_column(self, model_column_index):
        """
        Maps the model's column index to the actual dataframe column index,
        accounting for hidden columns.

        Args:
            model_column_index: The column index in the model's coordinate system

        Returns:
            The corresponding column index in the dataframe
        """
        # If no PandasDataFrame is associated, return the index as is
        if not self._pandas_df:
            return model_column_index

        # Count how many hidden columns are before this index
        offset = 0
        for hidden_col in sorted(self._pandas_df.hidden_columns):
            if hidden_col <= model_column_index + offset:
                offset += 1
            else:
                break

        # Return the actual column index in the dataframe
        return model_column_index + offset

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        """Return the header data for the given section and orientation."""
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            if section < 0 or section >= self.columnCount():
                return None
            # Map the model section to the actual dataframe column
            actual_col = self._map_to_actual_column(section)
            if actual_col < 0 or actual_col >= len(self._full_data.columns):
                return None
            return str(self._full_data.columns[actual_col])
        elif orientation == Qt.Orientation.Vertical and role == Qt.ItemDataRole.DisplayRole:
            if section < 0 or section >= len(self._full_data):
                return None
            return str(self._full_data.index[section])
        return None

    def canFetchMore(self, parent=QModelIndex()):
        """Return True if there is more data to fetch."""
        if parent.isValid():
            return False

        # Only allow fetching more data if explicitly triggered by vertical scrolling
        # or if auto-fetch is enabled
        can_fetch = self._visible_rows < len(self._full_data) and self._allow_auto_fetch
        print(f"canFetchMore called, result: {can_fetch}, visible: {self._visible_rows}, total: {len(self._full_data)}, auto_fetch: {self._allow_auto_fetch}")
        return can_fetch

    def fetchMore(self, parent=QModelIndex()):
        """Fetch more data by increasing the number of visible rows."""
        if parent.isValid():
            return

        # Calculate how many more rows to show
        remaining = len(self._full_data) - self._visible_rows
        items_to_fetch = min(self._chunk_size, remaining)

        if items_to_fetch <= 0:
            return

        print(f"fetchMore called, adding {items_to_fetch} rows")

        # Begin inserting rows
        start_row = self._visible_rows
        end_row = self._visible_rows + items_to_fetch

        self.beginInsertRows(QModelIndex(), start_row, end_row - 1)
        self._visible_rows = end_row
        self.endInsertRows()

        # Emit signal that data was loaded
        self.dataLoaded.emit(start_row, end_row)

        # Reset auto-fetch flag after fetching
        self._allow_auto_fetch = False

    def loadMoreData(self):
        """
        Explicitly request to load more data.
        This method is called from the view when vertical scrolling occurs.
        """
        # Enable auto-fetch temporarily
        self._allow_auto_fetch = True

        # Check if we can fetch more data
        if self.canFetchMore(QModelIndex()):
            print(f"Loading more data: currently showing {self._visible_rows} rows, loading next {self._chunk_size} rows")
            self.fetchMore(QModelIndex())
            return True
        else:
            # If we can't fetch more, check if we've reached the end of the data
            if self._visible_rows >= len(self._full_data):
                print(f"Already showing all {self._visible_rows} rows of data")
            else:
                print(f"Cannot fetch more data: visible={self._visible_rows}, total={len(self._full_data)}, auto_fetch={self._allow_auto_fetch}")
            return False

    def update_data(self, new_data, pandas_df=None):
        """
        Update the model's data and emit appropriate signals.

        Args:
            new_data (pd.DataFrame): The new data to display
            pandas_df (PandasDataFrame, optional): The PandasDataFrame wrapper that contains this dataframe
        """
        # Begin model reset to properly handle any structural changes
        self.beginResetModel()

        # Update internal data reference
        self._full_data = new_data
        self._visible_rows = min(self._chunk_size, len(new_data))
        self._allow_auto_fetch = False  # Ensure auto-fetch is disabled

        # Update PandasDataFrame reference if provided
        if pandas_df:
            self._pandas_df = pandas_df

        print(f"Model data updated, now showing {self._visible_rows} rows out of {len(new_data)}")

        # End model reset to notify the view that data has changed
        self.endResetModel()

        # Emit signal for initial load
        self.dataLoaded.emit(0, self._visible_rows)

    def hide_columns(self, column_indices):
        """
        Hide the specified columns from the view.

        Args:
            column_indices (list): List of column indices to hide
        """
        if not column_indices or not self._pandas_df:
            return

        # Begin model reset to properly handle structural changes
        self.beginResetModel()

        # Delegate to the PandasDataFrame
        self._pandas_df.hide_columns(column_indices)

        # End model reset to notify the view that data has changed
        self.endResetModel()

    def show_all_columns(self):
        """
        Make all hidden columns visible again.
        """
        if not self._pandas_df or not self._pandas_df.hidden_columns:
            return

        # Begin model reset to properly handle structural changes
        self.beginResetModel()

        # Delegate to the PandasDataFrame
        self._pandas_df.show_all_columns()

        # End model reset to notify the view that data has changed
        self.endResetModel()

    def get_actual_column_index(self, model_column_index):
        """
        Get the actual column index in the dataframe for a given model column index.

        Args:
            model_column_index: The column index in the model's coordinate system

        Returns:
            The corresponding column index in the dataframe
        """
        return self._map_to_actual_column(model_column_index)

    def get_hidden_columns_count(self):
        """
        Get the number of currently hidden columns.

        Returns:
            The number of hidden columns
        """
        if self._pandas_df:
            return self._pandas_df.get_hidden_columns_count()
        return 0
