import pandas as pd
import numpy as np

# DataFrame Operations
# [df_ETF基金规模] Import CSV from ETF基金规模.csv (#1)
df_ETF基金规模 = pd.read_csv('E:/git/desktop_pandas/data_files/ETF基金规模.csv')

# [df_ETF基金规模] Create line chart of '统计日期' vs '总规模（亿元）' (#2)
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

# Configure fonts to support Chinese characters
if platform.system() == 'Windows':
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
elif platform.system() == 'Darwin':  # macOS
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti', 'Heiti TC', 'Arial Unicode MS', 'sans-serif']
else:  # Linux and others
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Droid Sans Fallback', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False  # Ensure minus sign is displayed correctly

plt.figure(figsize=(10, 6))
plt.plot(df_ETF基金规模['统计日期'], df_ETF基金规模['总规模（亿元）'], label='总规模（亿元）')
plt.title('df_ETF基金规模 - line chart')
plt.xlabel('统计日期')
plt.ylabel('Values')
plt.legend()
plt.grid(True)
plt.show()
