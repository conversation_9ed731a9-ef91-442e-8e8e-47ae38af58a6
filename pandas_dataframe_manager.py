import pandas as pd
import os
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple

from pandas_dataframe import PandasDataFrame
from operation import Operation

class PandasDataFrameManager:
    """
    Central manager for all dataframe operations in the application.

    Responsibilities:
    - Managing the lifecycle of dataframes
    - Tracking operations on dataframes
    - Managing relationships between dataframes
    - Generating scripts from operations
    - Providing a unified interface for all dataframe-related functionality
    """

    def __init__(self, app=None):
        """
        Initialize the DataFrame Manager.

        Args:
            app: Reference to the main application (for sequence numbers)
        """
        self.dataframes: Dict[str, PandasDataFrame] = {}
        self.app = app
        self.last_executed_script = ""
        self._sequence_counter = 1
        self._dataframe_operations = {}

    def get_next_sequence_number(self) -> int:
        """Get the next sequence number for operations"""
        if self.app and hasattr(self.app, 'get_next_sequence_number'):
            return self.app.get_next_sequence_number()
        else:
            seq = self._sequence_counter
            self._sequence_counter += 1
            return seq

    def add_dataframe(self,
                     data: pd.DataFrame,
                     name: str,
                     source_path: str = "script",
                     parent: str = None,
                     add_load_operation: bool = True) -> PandasDataFrame:
        """
        Add a new dataframe to the manager.

        Args:
            data: The pandas DataFrame to wrap
            name: Name identifier for this dataframe
            source_path: Path to the source file or description of origin
            parent: Name of parent dataframe, if any
            add_load_operation: Whether to automatically add a load operation

        Returns:
            The created PandasDataFrame object
        """
        # Check if name already exists and create a unique name if needed
        original_name = name
        suffix = 0
        while name in self.dataframes:
            suffix += 1
            name = f"{original_name}_{suffix}"

        # Create and store the dataframe
        pandas_df = PandasDataFrame(
            data=data,
            name=name,
            source_path=source_path,
            add_load_operation=add_load_operation
        )

        # Set parent relationship if specified
        if parent and parent in self.dataframes:
            pandas_df.parent = parent

        self.dataframes[name] = pandas_df
        print(f"Added dataframe '{name}' with shape {data.shape}")

        return pandas_df

    def get_dataframe(self, name: str) -> Optional[PandasDataFrame]:
        """
        Get a dataframe by name.

        Args:
            name: Name of the dataframe to retrieve

        Returns:
            The PandasDataFrame object or None if not found
        """
        return self.dataframes.get(name)

    def remove_dataframe(self, name: str) -> bool:
        """
        Remove a dataframe from the manager.

        Args:
            name: Name of the dataframe to remove

        Returns:
            True if removed, False if not found
        """
        if name in self.dataframes:
            # Check if any other dataframes have this as a parent
            for df_name, df in list(self.dataframes.items()):
                if df.parent == name:
                    # Remove the parent reference
                    df.parent = None

            # Remove the dataframe
            del self.dataframes[name]
            print(f"Removed dataframe '{name}'")
            return True
        return False

    def get_all_dataframes(self) -> Dict[str, PandasDataFrame]:
        """
        Get all dataframes managed by this manager.

        Returns:
            Dictionary of name -> PandasDataFrame
        """
        return self.dataframes.copy()

    def add_operation(self,
                     df_name: str,
                     code: str,
                     operation_type: str = None,
                     description: str = None) -> Optional[Operation]:
        """
        Add an operation to a dataframe.

        Args:
            df_name: Name of the dataframe to add the operation to
            code: Python code representing the operation
            operation_type: Type of operation (e.g., "query", "column_add")
            description: Human-readable description of the operation

        Returns:
            The created Operation object or None if dataframe not found
        """
        if df_name not in self.dataframes:
            print(f"Cannot add operation: dataframe '{df_name}' not found")
            return None

        pandas_df = self.dataframes[df_name]

        # Create and add the operation
        seq_no = self.get_next_sequence_number()

        operation = Operation(
            code=code,
            seq_no=seq_no,
            source_df=pandas_df.parent if pandas_df.parent else df_name,
            target_df=df_name,
            description=description or "Operation",
            operation_type=operation_type or "generic"
        )

        pandas_df.operations.append(operation)
        print(f"Added operation #{seq_no} to '{df_name}': {operation_type or 'operation'}")

        return operation

    def get_operations(self, df_name: str) -> List[Operation]:
        """
        Get all operations for a dataframe.

        Args:
            df_name: Name of the dataframe

        Returns:
            List of Operation objects or empty list if dataframe not found
        """
        if df_name in self.dataframes:
            return self.dataframes[df_name].operations.copy()
        return []

    def update_dataframe(self, df_name: str, new_data: pd.DataFrame) -> bool:
        """
        Update the data in a dataframe.

        Args:
            df_name: Name of the dataframe to update
            new_data: New pandas DataFrame data

        Returns:
            True if updated, False if not found
        """
        if df_name in self.dataframes:
            self.dataframes[df_name].data = new_data
            print(f"Updated data for dataframe '{df_name}' with shape {new_data.shape}")
            return True
        return False

    def set_parent_relationship(self, child: str, parent: str) -> bool:
        """
        Set a parent-child relationship between dataframes.

        Args:
            child: Name of the child dataframe
            parent: Name of the parent dataframe

        Returns:
            True if relationship set, False if either dataframe not found
        """
        if child in self.dataframes and parent in self.dataframes:
            self.dataframes[child].parent = parent
            print(f"Set parent relationship: {parent} -> {child}")
            return True
        return False

    def generate_script(self, df_name: str) -> str:
        """
        Generate a Python script for a specific dataframe.
        This returns only the operations part without the fixture code.

        Args:
            df_name: Name of the dataframe

        Returns:
            Python script as a string, or empty string if dataframe not found
        """
        if df_name not in self.dataframes:
            return ""

        # Get only the operations part without imports, functions, or data loading
        return self.dataframes[df_name].generate_operations_code()

    def generate_full_script(self) -> str:
        """
        Generate a Python script that includes all operations across all dataframes.
        This returns only the operations part without the fixture code.

        Returns:
            Python script as a string containing only the operations
        """
        script = []

        # Collect all operations from all dataframes
        all_operations = []

        # Track seen operation sequence numbers to avoid duplicates
        seen_seq_numbers = set()

        for df_name, pandas_df in self.dataframes.items():
            # Add operations, but skip duplicates (operations with the same sequence number)
            for op in pandas_df.operations:
                if op.seq_no not in seen_seq_numbers:
                    all_operations.append(op)
                    seen_seq_numbers.add(op.seq_no)

        # Sort all operations by global sequence number
        sorted_operations = sorted(all_operations, key=lambda x: x.seq_no)

        # Add each operation with comments
        for op in sorted_operations:
            # Add a comment with the dataframe, operation description and sequence number
            target_df = op.target_df if op.target_df else "unknown"
            script.append(f"# [{target_df}] {op.description} (#{op.seq_no})")
            script.append(op.code)
            script.append("")  # Add empty line for readability

        return "\n".join(script)

    def generate_complete_script(self, code_manager=None) -> str:
        """
        Generate a complete script with fixture code and operations.
        If code_manager is provided, use its fixture code. Otherwise, generate fixture code.

        Args:
            code_manager: Optional CodeManager instance to get fixture code from

        Returns:
            Complete script as a string
        """
        # Generate operations code
        operations_code = self.generate_full_script()

        # If code manager is provided, use its fixture code
        if code_manager is not None:
            return code_manager.generate_combined_script(operations_code)

        # Otherwise, generate a basic fixture code ourselves
        fixture_code = [
            "import pandas as pd",
            "import numpy as np",
            ""
        ]

        # Add code to load source dataframes
        fixture_code.append("# Load source dataframes")
        dataframe_sources = {}
        loaded_dataframes = set()
        derived_dataframes = set()

        # Gather source paths
        for df_name, pandas_df in self.dataframes.items():
            dataframe_sources[df_name] = pandas_df.source_path
            if pandas_df.parent is not None:  # If it has a parent, it's derived
                derived_dataframes.add(df_name)

        # Load only the true source dataframes
        for df_name, source_path in dataframe_sources.items():
            # Skip derived dataframes
            if df_name in derived_dataframes:
                continue

            if source_path != "unknown" and "script" not in source_path:
                var_name = f"{df_name.replace('-', '_')}"
                fixture_code.append(f"{var_name} = pd.read_csv('{source_path}')")
                loaded_dataframes.add(df_name)

        fixture_code.append("")
        fixture_code.append("# DataFrame Operations")

        # Combine fixture code and operations
        return "\n".join(fixture_code) + "\n" + operations_code

    def generate_full_history_script(self, df_name: str) -> str:
        """
        Generate a script that shows the complete history of operations leading to this dataframe.

        This includes operations from parent dataframes to show the full chain of operations.

        Args:
            df_name: Name of the dataframe to generate history for

        Returns:
            Full history script as a string
        """
        if df_name not in self.dataframes:
            return ""

        # Start with imports
        script = [
            "import pandas as pd",
            "import numpy as np",
            ""
        ]

        # Get the current dataframe
        current_df = self.dataframes[df_name]

        # Track visited dataframes to avoid cycles
        visited = set()

        # Function to recursively add dataframe operations
        def add_dataframe_operations(df, script_lines):
            if df.name in visited:
                return

            visited.add(df.name)

            # If this dataframe has a parent, add its operations first
            if df.parent and df.parent in self.dataframes:
                add_dataframe_operations(self.dataframes[df.parent], script_lines)

            # Add code to load the dataframe if it's a source dataframe
            if df.source_path != "unknown" and "script" not in df.source_path and not df.parent:
                var_name = f"{df.name.replace('-', '_')}"
                script_lines.append(f"# Load {df.name} from source")
                script_lines.append(f"{var_name} = pd.read_csv('{df.source_path}')")
                script_lines.append("")

            # Add operations for this dataframe
            if df.operations:
                script_lines.append(f"# Operations for {df.name}")

                # Track seen operation sequence numbers to avoid duplicates
                operation_seq_numbers = set()

                for op in sorted(df.operations, key=lambda x: x.seq_no):
                    if op.seq_no in operation_seq_numbers:
                        continue

                    operation_seq_numbers.add(op.seq_no)

                    # Add comment with dataframe name, operation description and sequence number
                    script_lines.append(f"# [{df.name}] {op.description} (#{op.seq_no})")

                    # Handle query operations specially
                    if op.operation_type == "query" and op.code.startswith("df = df.query"):
                        script_lines.append(op.code)
                    else:
                        script_lines.append(op.code)

                    script_lines.append("")  # Add empty line for readability

        # Build the script
        add_dataframe_operations(current_df, script)

        # Add a clear indication of the current dataframe
        script.append(f"# Current dataframe: {df_name}")

        return "\n".join(script)

    def execute_script(self, script: str, interpreter=None, script_path=None):
        """
        Execute a Python script and update dataframes.

        Args:
            script: Python script to execute
            interpreter: Optional PythonInterpreter instance to use
            script_path: Path to the script file, used to set __file__ during execution

        Returns:
            Dictionary with execution results
        """
        from python_interpreter import PythonInterpreter

        # Save the script
        self.last_executed_script = script

        # Use provided interpreter or create a new one
        if interpreter is None:
            interpreter = PythonInterpreter()

        # Execute the script, passing the script path if provided
        result = interpreter.execute(script, script_path=script_path)

        if result['success']:
            # Process found dataframes
            new_dataframes = interpreter.convert_to_pandas_dataframes(self.app)
            df_found = False

            # Add or update dataframes
            for df_name, pandas_df in new_dataframes.items():
                df_found = True
                if df_name in self.dataframes:
                    # Update existing dataframe
                    existing_df = self.dataframes[df_name]
                    existing_df.data = pandas_df.data

                    # Preserve existing operations and add new ones
                    existing_op_codes = [op.code for op in existing_df.operations]
                    for new_op in pandas_df.operations:
                        if new_op.code not in existing_op_codes:
                            existing_df.operations.append(new_op)

                    # Update parent if needed
                    if pandas_df.parent and not existing_df.parent:
                        existing_df.parent = pandas_df.parent
                else:
                    # Add new dataframe to the manager
                    self.dataframes[df_name] = pandas_df

            # For ALL dataframes, attempt to extract operations from the script
            # This ensures even existing dataframes get operations when they're modified by a script
            if script:  # Only proceed if we have a script to analyze
                all_dataframes = list(self.dataframes.keys())
                print(f"Attempting to extract operations for {len(all_dataframes)} dataframes from script")

                # First, try to extract operations for all dataframes using our new script-order aware method
                # This ensures operations are preserved in the exact order they appear in the script
                for df_name in all_dataframes:
                    self._extract_operations_with_script_order(script, df_name)

                # Then check if we have any dataframes with zero operations and try again with aggressive mode
                for df_name in all_dataframes:
                    if len(self.dataframes[df_name].operations) == 0:
                        print(f"No operations found for {df_name}, attempting more aggressive extraction")
                        self._extract_operations_with_script_order(script, df_name, aggressive=True)

            # Also make sure to save the script - redundant but we want to be certain
            self.last_executed_script = script

            # Add dataframe discovery status to result
            result['df_found'] = df_found

        # Update global sequence counter to be higher than any operation sequence number
        if result['success'] and self.app and hasattr(self.app, 'global_seq_no'):
            # Find the highest sequence number used in any operation across all dataframes
            highest_seq_no = 0
            for df_name, df in self.dataframes.items():
                for op in df.operations:
                    if hasattr(op, 'seq_no') and op.seq_no > highest_seq_no:
                        highest_seq_no = op.seq_no

            # Update the global sequence counter if needed
            if highest_seq_no >= self.app.global_seq_no:
                print(f"Updating global sequence counter from {self.app.global_seq_no} to {highest_seq_no + 1}")
                self.app.global_seq_no = highest_seq_no + 1

        return result

    def _extract_operations_with_script_order(self, script: str, df_name: str, aggressive=False) -> None:
        """
        Extract operations from script and add them to dataframe, preserving the order of operations based on line numbers

        Args:
            script: Python script to parse
            df_name: Name of the dataframe to add operations to
            aggressive: If True, use more aggressive heuristics to find operations (currently unused)
        """
        # Get Python interpreter and analyze code
        from python_interpreter import PythonInterpreter
        interpreter = PythonInterpreter()
        analysis = interpreter.analyze_code(script)

        # Get operations that apply to this dataframe from the analysis
        operations = analysis.get("dataframe_operations", {})

        print(f"Processing dataframe operations from analysis... Found {len(operations)} dataframes with operations")

        # Log all dataframes found in analysis for better debugging
        print("Dataframes discovered in analysis:")
        for df in operations.keys():
            print(f"  - '{df}' with {len(operations[df])} operations")

        # Only process operations for the specific dataframe name
        if df_name in operations:
            print(f"\nProcessing operations for dataframe: '{df_name}'")
            df_operations = operations[df_name]
            print(f"  Operations found in analysis: {len(df_operations)}")

            # Log detailed operation information for debugging
            print(f"  Detailed operation breakdown for '{df_name}':")
            for i, op in enumerate(df_operations):
                op_type = op.get('type', 'unknown')
                line_num = op.get('node').lineno if 'node' in op else -1
                code_snippet = op.get('code', '')[:50] + '...' if len(op.get('code', '')) > 50 else op.get('code', '')
                print(f"    {i+1}. Type: {op_type}, Line: {line_num}, Code: {code_snippet}")

                # Show additional information for query operations
                if op_type == 'query' and 'query' in op:
                    print(f"       Query string: '{op['query']}'")
                    if 'parent' in op:
                        print(f"       Parent dataframe: '{op['parent']}'")


            # Sort operations by their line number in the script to preserve execution order
            df_operations.sort(key=lambda op: op.get('node').lineno if 'node' in op else 0)

            # Get the dataframe object
            pandas_df = self.dataframes[df_name]

            # Track operations added
            operations_added = 0

            # Add operations to dataframe with sequence numbers based on line numbers
            for i, op in enumerate(df_operations):
                # Get sequence number (use line number to preserve original script order)
                seq_num = op.get('node').lineno if 'node' in op else i + 1

                # Skip operations that are already there
                if any(existing_op.code == op.get('code', '') for existing_op in pandas_df.operations):
                    print(f"  Skipping duplicate operation: {op.get('code', '')[:50]}...")
                    continue

                # Determine operation type and description
                op_type = op.get('type', 'unknown')
                description = f"Operation {i+1} from script"

                if op_type == 'query' and 'query' in op:
                    description = f"Query: {op['query']}"
                elif op_type == 'column_rename':
                    description = "Column rename from script"
                elif op_type == 'file_import':
                    description = "Import data from script"
                elif op_type == 'groupby':
                    description = "GroupBy operation from script"
                elif op_type == 'filter':
                    description = "Filter operation from script"

                # Add operation to the dataframe
                pandas_df.add_operation(
                    code=op.get('code', ''),
                    seq_no=seq_num,
                    operation_type=op_type,
                    description=description
                )

                operations_added += 1

                # Enhanced logging for operation addition
                print(f"  Added operation: type={op_type}, seq={seq_num} to '{df_name}'")
                if op_type == 'query' and 'query' in op:
                    print(f"    Query: '{op['query']}'")

            # Log summary of operation additions
            print(f"\n  Summary: Added {operations_added} operations to '{df_name}'")
            print(f"  Total operations for '{df_name}': {len(pandas_df.operations)}")

            # Print the final sequence of operations to verify order
            print(f"\n  Final operation sequence for '{df_name}':")
            for i, op in enumerate(sorted(pandas_df.operations, key=lambda x: x.seq_no)):
                print(f"    {i+1}. seq={op.seq_no}, type={op.operation_type}")
            print()

    def _extract_operations_from_script(self, script: str, df_name: str, aggressive=False) -> None:
        """
        Extract operations from a script and add them to a dataframe.
        This is a wrapper around _extract_operations_with_script_order for backward compatibility.

        Args:
            script: Python script to parse
            df_name: Name of the dataframe to add operations to
            aggressive: If True, use more aggressive heuristics to find operations
        """
        # Call the newer implementation
        self._extract_operations_with_script_order(script, df_name, aggressive)

# The second implementation of _extract_operations_with_script_order has been removed
# as it was redundant and not used anywhere in the codebase.
# The first implementation (lines 492-571) is still in use.

    def get_last_executed_script(self) -> str:
        """
        Get the most recently executed script.

        Returns:
            The script as a string
        """
        return self.last_executed_script

    def load_csv(self, filepath: str, df_name: str = None) -> Optional[str]:
        """
        Load a CSV file and create a dataframe.

        Args:
            filepath: Path to the CSV file
            df_name: Optional name for the dataframe, defaults to filename

        Returns:
            Name of the created dataframe or None if loading failed
        """
        try:
            # Load the CSV file
            data = pd.read_csv(filepath)

            # Use filename as dataframe name if not provided
            if df_name is None:
                df_name = os.path.splitext(os.path.basename(filepath))[0]

            # Add the dataframe with add_load_operation=False to prevent duplicate load operations
            self.add_dataframe(
                data=data,
                name=df_name,
                source_path=filepath,
                add_load_operation=False
            )

            # Generate loading code
            var_name = f"{df_name.replace('-', '_')}"
            code = f"{var_name} = pd.read_csv('{filepath}')"

            # Add the operation
            self.add_operation(
                df_name=df_name,
                code=code,
                operation_type="file_import",
                description=f"Import CSV from {os.path.basename(filepath)}"
            )

            return df_name
        except Exception as e:
            print(f"Error loading CSV: {str(e)}")
            return None
