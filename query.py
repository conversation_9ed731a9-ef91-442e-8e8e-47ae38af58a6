class Query:
    """
    Represents a query operation in the application.
    
    This class encapsulates all information related to a query, including
    its name, criteria, and any additional metadata.
    """
    def __init__(self, name, criteria, description=None):
        """
        Initialize a Query object.
        
        Args:
            name (str): Name of the query for display and reference
            criteria (str): Query criteria string (can contain multiple lines)
            description (str, optional): Additional description of the query
        """
        self.name = name
        self.criteria = criteria
        self.description = description or ""
    
    def __str__(self):
        """String representation of the query"""
        criterion1 = self.criteria.split('\n')[0]
        return f"{self.name}: {criterion1}"
    
    def to_dict(self):
        """Convert query to dictionary for serialization"""
        return {
            "name": self.name,
            "criteria": self.criteria,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create query from dictionary"""
        return cls(
            name=data["name"],
            criteria=data["criteria"],
            description=data.get("description", "")
        )
