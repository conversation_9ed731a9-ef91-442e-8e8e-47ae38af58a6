import pandas as pd
import numpy as np
from pathlib import Path

# Data Loading
df_HK_800700_5min_candles = pd.read_csv(Path(__file__).parent.parent / 'data_files/HK_800700_5min_candles.csv')

# DataFrame Operations
# Add/modify column: time_date (#9)
df_HK_800700_5min_candles['time_date'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.date

# Add/modify column: time_hour (#10)
df_HK_800700_5min_candles['time_hour'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.hour

# Add/modify column: time_minute (#11)
df_HK_800700_5min_candles['time_minute'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.minute

# Add/modify column: time_hour_*60.0 (#12)
df_HK_800700_5min_candles['time_hour_*60.0'] = df_HK_800700_5min_candles['time_hour'] * 60.0

# Add/modify column: time_hour_*60.0_+_time_minute (#13)
df_HK_800700_5min_candles['time_hour_*60.0_+_time_minute'] = df_HK_800700_5min_candles['time_hour_*60.0'] + df_HK_800700_5min_candles['time_minute']

# Operation: rename (#14)
df_HK_800700_5min_candles.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)

# Apply groupby transform on 'open' grouped by 'time_date' (#15)
df_HK_800700_5min_candles['open_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['open'].transform('first')

# Renamed column 'open_groupby_time_date' to 'day_open' (#16)
df_HK_800700_5min_candles.rename(columns={'open_groupby_time_date': 'day_open'}, inplace=True)

# Apply groupby transform on 'close' grouped by 'time_date' (#17)
df_HK_800700_5min_candles['close_groupby_time_date'] = df_HK_800700_5min_candles.groupby('time_date')['close'].transform('last')

# Renamed column 'close_groupby_time_date' to 'day_close' (#18)
df_HK_800700_5min_candles.rename(columns={'close_groupby_time_date': 'day_close'}, inplace=True)

# Apply rolling mean with window size 6 to 'close' (#19)
df_HK_800700_5min_candles['close_rolling_6_mean'] = df_HK_800700_5min_candles['close'].rolling(window=6).mean()

# Renamed column 'close_rolling_6_mean' to 'close_MA6' (#20)
df_HK_800700_5min_candles.rename(columns={'close_rolling_6_mean': 'close_MA6'}, inplace=True)

# Apply rolling mean with window size 12 to 'close' (#21)
df_HK_800700_5min_candles['close_rolling_12_mean'] = df_HK_800700_5min_candles['close'].rolling(window=12).mean()

# Renamed column 'close_rolling_12_mean' to 'close_MA12' (#22)
df_HK_800700_5min_candles.rename(columns={'close_rolling_12_mean': 'close_MA12'}, inplace=True)

# Apply rolling mean with window size 24 to 'close' (#23)
df_HK_800700_5min_candles['close_rolling_24_mean'] = df_HK_800700_5min_candles['close'].rolling(window=24).mean()

# Renamed column 'close_rolling_24_mean' to 'close_MA24' (#24)
df_HK_800700_5min_candles.rename(columns={'close_rolling_24_mean': 'close_MA24'}, inplace=True)

# Apply rolling mean with window size 48 to 'close' (#25)
df_HK_800700_5min_candles['close_rolling_48_mean'] = df_HK_800700_5min_candles['close'].rolling(window=48).mean()

# Renamed column 'close_rolling_48_mean' to 'close_MA48' (#26)
df_HK_800700_5min_candles.rename(columns={'close_rolling_48_mean': 'close_MA48'}, inplace=True)
