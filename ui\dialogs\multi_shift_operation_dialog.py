from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSpinBox, QPushButton, QFormLayout, QLineEdit,
                            QListWidget, QListWidgetItem, QCheckBox)
from PyQt6.QtCore import Qt

class MultiShiftOperationDialog(QDialog):
    """
    Dialog for configuring a shift operation on multiple dataframe columns.
    """
    def __init__(self, parent, column_names):
        """
        Initialize the multi-column shift operation dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            column_names: List of names of the columns to shift
        """
        super().__init__(parent)
        self.parent = parent
        self.column_names = column_names
        self.periods = 0
        self.result_column_names = {col: f"{col}_shift0" for col in column_names}
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle(f"Shift Multiple Columns")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Display selected columns
        columns_label = QLabel("Selected columns:")
        self.columns_list = QListWidget()
        for column_name in self.column_names:
            item = QListWidgetItem(column_name)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(Qt.CheckState.Checked)
            self.columns_list.addItem(item)
        
        form_layout.addRow(columns_label, self.columns_list)

        # Periods input
        self.periods_spin = QSpinBox()
        self.periods_spin.setRange(-10000, 10000)
        self.periods_spin.setValue(0)
        self.periods_spin.setSingleStep(1)
        form_layout.addRow("Shift periods:", self.periods_spin)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add description
        description = QLabel("Positive values shift down (lag), negative values shift up (lead).")
        description.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(description)

        # Add some spacing
        main_layout.addSpacing(15)

        # Add buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Connect buttons
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.accept)
        
        # Connect periods spin box to update result names
        self.periods_spin.valueChanged.connect(self.update_result_names)
        
    def update_result_names(self):
        """Update the result column names based on the current periods value."""
        periods = self.periods_spin.value()
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            column_name = item.text()
            self.result_column_names[column_name] = f"{column_name}_shift{periods}"
        
    def accept(self):
        """Handle dialog acceptance and store the values."""
        self.periods = self.periods_spin.value()
        
        # Update the list of selected columns based on checkbox state
        self.selected_columns = []
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                self.selected_columns.append(item.text())
        
        # Update result column names one final time
        self.update_result_names()
        
        super().accept()
