import pandas as pd
import numpy as np

# DataFrame Operations
# Import CSV from sse_market_overview_history.csv (#1)
df_sse_market_overview_history = pd.read_csv('E:/git/desktop_pandas/data_files/sse_market_overview_history.csv')

# Extract date from '日期' (#2)
df_sse_market_overview_history['日期_date'] = pd.to_datetime(df_sse_market_overview_history['日期']).dt.date

# Calculate natural log of '股票流通市值' (#3)
df_sse_market_overview_history['股票流通市值_log'] = np.log(pd.to_numeric(df_sse_market_overview_history['股票流通市值'], errors='coerce'))

# Calculate natural log of 'ETF流通市值' (#4)
df_sse_market_overview_history['ETF流通市值_log'] = np.log(pd.to_numeric(df_sse_market_overview_history['ETF流通市值'], errors='coerce'))

# Calculate difference of multiple columns: '股票流通市值_log', 'ETF流通市值_log' with 1 period(s) (#5)
df_sse_market_overview_history['股票流通市值_log_diff1'] = df_sse_market_overview_history['股票流通市值_log'].diff(1)
df_sse_market_overview_history['ETF流通市值_log_diff1'] = df_sse_market_overview_history['ETF流通市值_log'].diff(1)

# Perform - operation on '股票流通市值_log' (#6)
df_sse_market_overview_history['股票流通市值_log_-_股票流通市值_log_0'] = df_sse_market_overview_history['股票流通市值_log'] - df_sse_market_overview_history.loc[0, '股票流通市值_log']

# Perform - operation on 'ETF流通市值_log' (#7)
df_sse_market_overview_history['ETF流通市值_log_-_ETF流通市值_log_0'] = df_sse_market_overview_history['ETF流通市值_log'] - df_sse_market_overview_history.loc[0, 'ETF流通市值_log']
