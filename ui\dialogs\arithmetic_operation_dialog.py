from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                            QComboBox, QPushButton, QRadioButton, QButtonGroup, QFormLayout,
                            QSpinBox)
from PyQt6.QtCore import Qt

class ArithmeticOperationDialog(QDialog):
    """
    Dialog for configuring an arithmetic operation on a dataframe column.
    """
    def __init__(self, parent, column_name, operation, dataframe_columns):
        """
        Initialize the arithmetic operation dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            column_name: The name of the column to perform the operation on
            operation: The arithmetic operation to apply (+, -, *, /)
            dataframe_columns: List of column names in the dataframe
        """
        super().__init__(parent)
        self.parent = parent
        self.column_name = column_name
        self.operation = operation
        self.dataframe_columns = dataframe_columns
        
        # Operation symbols to text mapping
        self.operation_text = {
            "+": "Addition",
            "-": "Subtraction",
            "*": "Multiplication",
            "/": "Division"
        }
        
        # Initialize result variables
        self.use_value = True
        self.use_column = False
        self.use_cell = False
        self.operand_value = None
        self.operand_column = None
        self.cell_column = None
        self.cell_index = None
        self.new_column_name = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle(f"{self.operation_text.get(self.operation, 'Arithmetic')} Operation: {self.column_name}")
        self.setMinimumWidth(450)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Column name display with consistent styling
        column_label = QLabel("Column:")
        column_value = QLabel(self.column_name)
        column_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(column_label, column_value)

        # Operation display
        operation_label = QLabel("Operation:")
        operation_value = QLabel(f"{self.column_name} {self.operation} ?")
        operation_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(operation_label, operation_value)

        # Add radio buttons for operand type in a horizontal layout
        operand_type_layout = QHBoxLayout()
        self.operand_type_group = QButtonGroup(self)
        self.value_radio = QRadioButton("Value")
        self.value_radio.setChecked(True)
        self.column_radio = QRadioButton("Column")
        self.cell_radio = QRadioButton("Cell")

        self.operand_type_group.addButton(self.value_radio, 1)
        self.operand_type_group.addButton(self.column_radio, 2)
        self.operand_type_group.addButton(self.cell_radio, 3)

        operand_type_layout.addWidget(self.value_radio)
        operand_type_layout.addWidget(self.column_radio)
        operand_type_layout.addWidget(self.cell_radio)
        form_layout.addRow("Operand type:", operand_type_layout)

        # Add value input
        self.value_input = QLineEdit()
        self.value_input.setPlaceholderText("Enter a numeric value")
        form_layout.addRow("Value:", self.value_input)

        # Add column selection
        self.column_combo = QComboBox()
        self.column_combo.addItems(self.dataframe_columns)
        self.column_combo.setEnabled(False)
        form_layout.addRow("Column:", self.column_combo)
        
        # Add cell selection (column and index)
        self.cell_column_combo = QComboBox()
        self.cell_column_combo.addItems(self.dataframe_columns)
        self.cell_column_combo.setEnabled(False)
        form_layout.addRow("Cell column:", self.cell_column_combo)
        
        self.cell_index_spin = QSpinBox()
        self.cell_index_spin.setMinimum(0)
        self.cell_index_spin.setMaximum(9999)  # Set a reasonable maximum
        self.cell_index_spin.setEnabled(False)
        form_layout.addRow("Cell index (row):", self.cell_index_spin)

        # Result column name preview
        self.result_name_edit = QLineEdit()
        self.result_name_edit.setPlaceholderText("Auto-generated based on operation")
        form_layout.addRow("Result column name:", self.result_name_edit)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add description
        description = QLabel(f"Creates a new column by applying the {self.operation_text.get(self.operation, 'arithmetic')} operation.")
        description.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(description)

        # Add some spacing
        main_layout.addSpacing(15)

        # Connect radio buttons to enable/disable appropriate inputs
        self.value_radio.toggled.connect(self.update_input_state)
        self.column_radio.toggled.connect(self.update_input_state)
        self.cell_radio.toggled.connect(self.update_input_state)
        self.value_input.textChanged.connect(self.update_input_state)
        self.column_combo.currentTextChanged.connect(self.update_input_state)
        self.cell_column_combo.currentTextChanged.connect(self.update_input_state)
        self.cell_index_spin.valueChanged.connect(self.update_input_state)

        # Initialize the result column name
        self.update_input_state()

        # Add buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Connect buttons
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.accept)
        
    def update_input_state(self):
        """Update the UI state based on the selected operand type."""
        # Enable/disable inputs based on selected radio button
        self.value_input.setEnabled(self.value_radio.isChecked())
        self.column_combo.setEnabled(self.column_radio.isChecked())
        self.cell_column_combo.setEnabled(self.cell_radio.isChecked())
        self.cell_index_spin.setEnabled(self.cell_radio.isChecked())
        
        # Update result column name preview
        if self.value_radio.isChecked():
            try:
                operand_value = self.value_input.text()
                if operand_value != "":
                    self.result_name_edit.setText(f"{self.column_name}_{self.operation}{operand_value}")
                else:
                    self.result_name_edit.setText("")
            except ValueError:
                self.result_name_edit.setText("")
        elif self.column_radio.isChecked():
            operand_column = self.column_combo.currentText()
            if operand_column:
                self.result_name_edit.setText(f"{self.column_name}_{self.operation}_{operand_column}")
            else:
                self.result_name_edit.setText("")
        elif self.cell_radio.isChecked():
            cell_column = self.cell_column_combo.currentText()
            cell_index = self.cell_index_spin.value()
            if cell_column:
                self.result_name_edit.setText(f"{self.column_name}_{self.operation}_{cell_column}_{cell_index}")
            else:
                self.result_name_edit.setText("")
                
    def accept(self):
        """Handle dialog acceptance and store the values."""
        try:
            self.use_value = self.value_radio.isChecked()
            self.use_column = self.column_radio.isChecked()
            self.use_cell = self.cell_radio.isChecked()
            
            if self.use_value:
                # Get value from input
                operand_text = self.value_input.text()
                try:
                    # Try to convert to number
                    self.operand_value = float(operand_text)
                    is_numeric = True
                except ValueError:
                    # If not a number, show error
                    self.parent.status_bar.showMessage("Error: Value must be a number")
                    return
                    
                # Use user-provided name if available, otherwise use auto-generated name
                if self.result_name_edit.text():
                    self.new_column_name = self.result_name_edit.text()
                else:
                    self.new_column_name = f"{self.column_name}_{self.operation}{self.operand_value}"
            elif self.use_column:
                # Get column from combo box
                self.operand_column = self.column_combo.currentText()
                
                # Use user-provided name if available, otherwise use auto-generated name
                if self.result_name_edit.text():
                    self.new_column_name = self.result_name_edit.text()
                else:
                    self.new_column_name = f"{self.column_name}_{self.operation}_{self.operand_column}"
            elif self.use_cell:
                # Get cell column and index
                self.cell_column = self.cell_column_combo.currentText()
                self.cell_index = self.cell_index_spin.value()
                
                # Use user-provided name if available, otherwise use auto-generated name
                if self.result_name_edit.text():
                    self.new_column_name = self.result_name_edit.text()
                else:
                    self.new_column_name = f"{self.column_name}_{self.operation}_{self.cell_column}_{self.cell_index}"
                    
            super().accept()
        except Exception as e:
            self.parent.status_bar.showMessage(f"Error in arithmetic operation dialog: {str(e)}")
            
    def get_operation_code(self, var_name):
        """
        Get the operation code for the arithmetic operation.
        
        Args:
            var_name: The variable name for the dataframe
            
        Returns:
            The operation code as a string
        """
        if self.use_value:
            if self.operation == "+":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] + {self.operand_value}"
            elif self.operation == "-":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] - {self.operand_value}"
            elif self.operation == "*":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] * {self.operand_value}"
            elif self.operation == "/":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] / {self.operand_value}"
        elif self.use_column:
            if self.operation == "+":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] + {var_name}['{self.operand_column}']"
            elif self.operation == "-":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] - {var_name}['{self.operand_column}']"
            elif self.operation == "*":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] * {var_name}['{self.operand_column}']"
            elif self.operation == "/":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] / {var_name}['{self.operand_column}']"
        elif self.use_cell:
            if self.operation == "+":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] + {var_name}.loc[{self.cell_index}, '{self.cell_column}']"
            elif self.operation == "-":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] - {var_name}.loc[{self.cell_index}, '{self.cell_column}']"
            elif self.operation == "*":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] * {var_name}.loc[{self.cell_index}, '{self.cell_column}']"
            elif self.operation == "/":
                return f"{var_name}['{self.new_column_name}'] = {var_name}['{self.column_name}'] / {var_name}.loc[{self.cell_index}, '{self.cell_column}']"
        
        return ""
