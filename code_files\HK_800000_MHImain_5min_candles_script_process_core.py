import pandas as pd
import numpy as np
from pathlib import Path

# Data Loading
df_HK_800000_5min_candles = pd.read_csv(Path(__file__).parent.parent / 'data_files/HK_800000_5min_candles_20241101_20250522.csv')

# Data Loading
df_HK_MHImain_5min_candles = pd.read_csv(Path(__file__).parent.parent / 'data_files/HK_MHImain_5min_candles_20241101_20250522.csv')

# DataFrame Operations
# Add/modify column: time_date (#9)
df_HK_800000_5min_candles['time_date'] = pd.to_datetime(df_HK_800000_5min_candles['time']).dt.date

# Add/modify column: time_hour (#10)
df_HK_800000_5min_candles['time_hour'] = pd.to_datetime(df_HK_800000_5min_candles['time']).dt.hour

# Add/modify column: time_minute (#11)
df_HK_800000_5min_candles['time_minute'] = pd.to_datetime(df_HK_800000_5min_candles['time']).dt.minute

# Add/modify column: time_hour_*60.0 (#12)
df_HK_800000_5min_candles['time_hour_*60.0'] = df_HK_800000_5min_candles['time_hour'] * 60.0

# Add/modify column: time_hour_*60.0_+_time_minute (#13)
df_HK_800000_5min_candles['time_hour_*60.0_+_time_minute'] = df_HK_800000_5min_candles['time_hour_*60.0'] + df_HK_800000_5min_candles['time_minute']

# Operation: rename (#14)
df_HK_800000_5min_candles.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)

# Add/modify column: open_groupby_time_date (#15)
df_HK_800000_5min_candles['open_groupby_time_date'] = df_HK_800000_5min_candles.groupby('time_date')['open'].transform('first')

# Operation: rename (#16)
df_HK_800000_5min_candles.rename(columns={'open_groupby_time_date': 'day_open'}, inplace=True)

# Add/modify column: close_groupby_time_date (#17)
df_HK_800000_5min_candles['close_groupby_time_date'] = df_HK_800000_5min_candles.groupby('time_date')['close'].transform('last')

# Operation: rename (#18)
df_HK_800000_5min_candles.rename(columns={'close_groupby_time_date': 'day_close'}, inplace=True)

# Add/modify column: close_rolling_6_mean (#19)
df_HK_800000_5min_candles['close_rolling_6_mean'] = df_HK_800000_5min_candles['close'].rolling(window=6).mean()

# Operation: rename (#20)
df_HK_800000_5min_candles.rename(columns={'close_rolling_6_mean': 'close_MA6'}, inplace=True)

# Add/modify column: close_rolling_12_mean (#21)
df_HK_800000_5min_candles['close_rolling_12_mean'] = df_HK_800000_5min_candles['close'].rolling(window=12).mean()

# Operation: rename (#22)
df_HK_800000_5min_candles.rename(columns={'close_rolling_12_mean': 'close_MA12'}, inplace=True)

# Add/modify column: close_rolling_24_mean (#23)
df_HK_800000_5min_candles['close_rolling_24_mean'] = df_HK_800000_5min_candles['close'].rolling(window=24).mean()

# Operation: rename (#24)
df_HK_800000_5min_candles.rename(columns={'close_rolling_24_mean': 'close_MA24'}, inplace=True)

# Add/modify column: close_rolling_48_mean (#25)
df_HK_800000_5min_candles['close_rolling_48_mean'] = df_HK_800000_5min_candles['close'].rolling(window=48).mean()

# Operation: rename (#26)
df_HK_800000_5min_candles.rename(columns={'close_rolling_48_mean': 'close_MA48'}, inplace=True)

# Add/modify column: close_shift1 (#27)
df_HK_800000_5min_candles['close_shift1'] = df_HK_800000_5min_candles['close'].shift(1)

# Add/modify column: close_shift1_groupby_time_date (#28)
df_HK_800000_5min_candles['close_shift1_groupby_time_date'] = df_HK_800000_5min_candles.groupby('time_date')['close_shift1'].transform('first')

# Operation: rename (#29)
df_HK_800000_5min_candles.rename(columns={'close_shift1_groupby_time_date': 'pre_day_close'}, inplace=True)

# DataFrame Operations
# Add/modify column: time_date (#9)
df_HK_MHImain_5min_candles['time_date'] = pd.to_datetime(df_HK_MHImain_5min_candles['time']).dt.date

# Add/modify column: time_hour (#10)
df_HK_MHImain_5min_candles['time_hour'] = pd.to_datetime(df_HK_MHImain_5min_candles['time']).dt.hour

# Add/modify column: time_minute (#11)
df_HK_MHImain_5min_candles['time_minute'] = pd.to_datetime(df_HK_MHImain_5min_candles['time']).dt.minute

# Add/modify column: time_hour_*60.0 (#12)
df_HK_MHImain_5min_candles['time_hour_*60.0'] = df_HK_MHImain_5min_candles['time_hour'] * 60.0

# Add/modify column: time_hour_*60.0_+_time_minute (#13)
df_HK_MHImain_5min_candles['time_hour_*60.0_+_time_minute'] = df_HK_MHImain_5min_candles['time_hour_*60.0'] + df_HK_MHImain_5min_candles['time_minute']

# Operation: rename (#14)
df_HK_MHImain_5min_candles.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)

# Add/modify column: open_groupby_time_date (#15)
df_HK_MHImain_5min_candles['open_groupby_time_date'] = df_HK_MHImain_5min_candles.groupby('time_date')['open'].transform('first')

# Operation: rename (#16)
df_HK_MHImain_5min_candles.rename(columns={'open_groupby_time_date': 'day_open'}, inplace=True)

# Add/modify column: close_groupby_time_date (#17)
df_HK_MHImain_5min_candles['close_groupby_time_date'] = df_HK_MHImain_5min_candles.groupby('time_date')['close'].transform('last')

# Operation: rename (#18)
df_HK_MHImain_5min_candles.rename(columns={'close_groupby_time_date': 'day_close'}, inplace=True)

# Add/modify column: close_rolling_6_mean (#19)
df_HK_MHImain_5min_candles['close_rolling_6_mean'] = df_HK_MHImain_5min_candles['close'].rolling(window=6).mean()

# Operation: rename (#20)
df_HK_MHImain_5min_candles.rename(columns={'close_rolling_6_mean': 'close_MA6'}, inplace=True)

# Add/modify column: close_rolling_12_mean (#21)
df_HK_MHImain_5min_candles['close_rolling_12_mean'] = df_HK_MHImain_5min_candles['close'].rolling(window=12).mean()

# Operation: rename (#22)
df_HK_MHImain_5min_candles.rename(columns={'close_rolling_12_mean': 'close_MA12'}, inplace=True)

# Add/modify column: close_rolling_24_mean (#23)
df_HK_MHImain_5min_candles['close_rolling_24_mean'] = df_HK_MHImain_5min_candles['close'].rolling(window=24).mean()

# Operation: rename (#24)
df_HK_MHImain_5min_candles.rename(columns={'close_rolling_24_mean': 'close_MA24'}, inplace=True)

# Add/modify column: close_rolling_48_mean (#25)
df_HK_MHImain_5min_candles['close_rolling_48_mean'] = df_HK_MHImain_5min_candles['close'].rolling(window=48).mean()

# Operation: rename (#26)
df_HK_MHImain_5min_candles.rename(columns={'close_rolling_48_mean': 'close_MA48'}, inplace=True)

# Add/modify column: close_shift1 (#27)
df_HK_MHImain_5min_candles['close_shift1'] = df_HK_MHImain_5min_candles['close'].shift(1)

# Add/modify column: close_shift1_groupby_time_date (#28)
df_HK_MHImain_5min_candles['close_shift1_groupby_time_date'] = df_HK_MHImain_5min_candles.groupby('time_date')['close_shift1'].transform('first')

# Operation: rename (#29)
df_HK_MHImain_5min_candles.rename(columns={'close_shift1_groupby_time_date': 'pre_day_close'}, inplace=True)
