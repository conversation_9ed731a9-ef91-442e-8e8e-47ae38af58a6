from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFormLayout, QLineEdit,
                            QListWidget, QListWidgetItem, QWidget, QCheckBox)
from PyQt6.QtCore import Qt

class MaxOperationDialog(QDialog):
    """
    Dialog for configuring a max operation on multiple dataframe columns.
    """
    def __init__(self, parent, dataframe_columns, initially_selected_columns=None):
        """
        Initialize the max operation dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            dataframe_columns: List of all column names in the dataframe
            initially_selected_columns: List of initially selected column names (optional)
        """
        super().__init__(parent)
        self.parent = parent
        self.dataframe_columns = dataframe_columns
        self.initially_selected_columns = initially_selected_columns or []
        self.selected_columns = []
        self.result_column_name = "max_result"
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle("Max Operation - Select Columns")
        self.setMinimumWidth(500)
        self.setMinimumHeight(450)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Description
        description = QLabel("Select columns to calculate row-wise maximum values:")
        description.setStyleSheet("font-weight: bold; color: #333;")
        main_layout.addWidget(description)

        # Add some spacing
        main_layout.addSpacing(10)

        # Available columns list with checkboxes
        columns_label = QLabel("Available columns:")
        self.columns_list = QListWidget()
        self.columns_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #cccccc;
                font-size: 14px;
                padding: 2px;
            }
            QListWidget::item:selected {
                background-color: white;
                font-size: 14px;
                color: black;
            }
            QListWidget::item {
                padding: 6px;
                margin-left: 6px;
                min-height: 24px;
            }
        """)
        
        # Add all columns with checkboxes
        for column_name in self.dataframe_columns:
            item = QListWidgetItem(column_name)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            # Check if this column was initially selected
            if column_name in self.initially_selected_columns:
                item.setCheckState(Qt.CheckState.Checked)
            else:
                item.setCheckState(Qt.CheckState.Unchecked)
            self.columns_list.addItem(item)
        
        form_layout.addRow(columns_label, self.columns_list)

        # Select/Deselect All buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(4)  # Reduce spacing
        
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.select_all_btn.clicked.connect(self.select_all_columns)
        buttons_layout.addWidget(self.select_all_btn)
        
        self.deselect_all_btn = QPushButton("Deselect All")
        self.deselect_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)
        self.deselect_all_btn.clicked.connect(self.deselect_all_columns)
        buttons_layout.addWidget(self.deselect_all_btn)
        
        buttons_layout.addStretch()  # Push buttons to the left
        
        # Create a widget for the buttons layout
        buttons_widget = QWidget()
        buttons_widget.setLayout(buttons_layout)
        form_layout.addRow("", buttons_widget)

        # Result column name
        self.result_name_edit = QLineEdit()
        self.result_name_edit.setText(self.result_column_name)
        self.result_name_edit.setPlaceholderText("Enter name for the result column")
        self.result_name_edit.textChanged.connect(self.update_result_name)
        form_layout.addRow("Result column name:", self.result_name_edit)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add description
        operation_description = QLabel("Calculates the maximum value across selected columns for each row (axis=1).")
        operation_description.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(operation_description)

        # Add some spacing
        main_layout.addSpacing(15)

        # Dialog buttons
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("OK")
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #616161;
            }
            QPushButton:pressed {
                background-color: #424242;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
        
    def select_all_columns(self):
        """Select all columns in the list"""
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            item.setCheckState(Qt.CheckState.Checked)

    def deselect_all_columns(self):
        """Deselect all columns in the list"""
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            item.setCheckState(Qt.CheckState.Unchecked)
            
    def update_result_name(self):
        """Update the result column name based on user input."""
        self.result_column_name = self.result_name_edit.text().strip()
        
    def accept(self):
        """Handle dialog acceptance and store the values."""
        # Update the list of selected columns based on checkbox state
        self.selected_columns = []
        for i in range(self.columns_list.count()):
            item = self.columns_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                self.selected_columns.append(item.text())
        
        # Validate that at least one column is selected
        if not self.selected_columns:
            # Show error message or just return without accepting
            return
        
        # Update result column name
        self.result_column_name = self.result_name_edit.text().strip()
        if not self.result_column_name:
            self.result_column_name = "max_result"
        
        super().accept()
