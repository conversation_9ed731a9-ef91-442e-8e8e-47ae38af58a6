from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFormLayout)
from PyQt6.QtCore import Qt

class RenameColumnDialog(QDialog):
    """
    Dialog for renaming a column in a dataframe.
    """
    def __init__(self, parent, column_name):
        """
        Initialize the rename column dialog.
        
        Args:
            parent: The parent widget (typically the main application)
            column_name: The current name of the column to be renamed
        """
        super().__init__(parent)
        self.parent = parent
        self.old_name = column_name
        self.new_name = column_name
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        self.setWindowTitle(f"Rename Column: {self.old_name}")
        self.setMinimumWidth(450)

        # Main layout
        main_layout = QVBoxLayout()

        # Create form layout for consistent field alignment
        form_layout = QFormLayout()
        form_layout.setSpacing(10)  # Add spacing between rows

        # Current column name display
        current_name_label = QLabel("Current name:")
        current_name_value = QLabel(self.old_name)
        current_name_value.setStyleSheet("font-weight: bold;")
        form_layout.addRow(current_name_label, current_name_value)

        # New name input
        self.new_name_input = QLineEdit()
        self.new_name_input.setPlaceholderText("Enter new column name")
        self.new_name_input.setText(self.old_name)  # Pre-fill with current name for easier editing
        self.new_name_input.selectAll()  # Select all text for easy replacement
        form_layout.addRow("New name:", self.new_name_input)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        # Add description
        description = QLabel("Rename the column while preserving all data.")
        description.setStyleSheet("color: #666; font-style: italic;")
        main_layout.addWidget(description)

        # Add some spacing
        main_layout.addSpacing(15)

        # Add buttons
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Connect buttons
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.accept)
        self.new_name_input.returnPressed.connect(self.accept)  # Allow pressing Enter to confirm
        
    def accept(self):
        """Handle dialog acceptance and store the new column name."""
        self.new_name = self.new_name_input.text().strip()
        
        # Validate the new name
        if not self.new_name:
            self.parent.status_bar.showMessage("Error: New column name cannot be empty")
            return
            
        # If the name hasn't changed, just accept the dialog
        if self.new_name == self.old_name:
            super().accept()
            return
            
        super().accept()
