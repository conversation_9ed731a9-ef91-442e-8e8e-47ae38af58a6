class Operation:
    """
    Represents a data transformation operation in the application.
    """
    def __init__(self, code, seq_no, source_df=None, target_df=None, description=None, operation_type=None):
        """
        Initialize an Operation object.
        
        Args:
            code (str): Python code representing the operation
            seq_no (int): Sequence number for execution order
            source_df (str, optional): Name of source dataframe
            target_df (str, optional): Name of target dataframe
            description (str, optional): Human-readable description
            operation_type (str, optional): Type of operation (e.g., "query", "arithmetic", "datetime")
        """
        self.code = code
        self.seq_no = seq_no
        self.source_df = source_df
        self.target_df = target_df
        self.description = description or "Unnamed operation"
        self.operation_type = operation_type or "generic"
        self.timestamp = None  # Could add timestamp if needed
        
    def __str__(self):
        """String representation of the operation"""
        return f"#{self.seq_no}: {self.description} ({self.operation_type})"
    
    def to_dict(self):
        """Convert operation to dictionary for serialization"""
        return {
            "code": self.code,
            "seq_no": self.seq_no,
            "source_df": self.source_df,
            "target_df": self.target_df,
            "description": self.description,
            "operation_type": self.operation_type
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create operation from dictionary"""
        return cls(
            code=data["code"],
            seq_no=data["seq_no"],
            source_df=data.get("source_df"),
            target_df=data.get("target_df"),
            description=data.get("description"),
            operation_type=data.get("operation_type")
        )
    
    def execute(self, namespace):
        """
        Execute the operation in the given namespace.
        
        Args:
            namespace (dict): Dictionary of variables for execution context
            
        Returns:
            Any: Result of the operation
        """
        try:
            # Execute the code in the provided namespace
            exec(self.code, namespace)
            
            # If target_df is specified, return the corresponding variable
            if self.target_df and self.target_df in namespace:
                return namespace[self.target_df]
            
            return None
        except Exception as e:
            raise RuntimeError(f"Error executing operation {self.seq_no}: {str(e)}")