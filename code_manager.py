from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                            QPushButton, QFileDialog, QWidget, QLabel,
                            QRadioButton, QButtonGroup, QTabWidget)
import pandas as pd
import numpy as np
import os
import sys
from python_interpreter import PythonInterpreter

class CodeManager:
    def __init__(self, main_app):
        self.main_app = main_app
        self.current_script = ""
        self.script_file_path = None  # Store the path of the loaded script
        self.script_operations = {}  # Store operations by dataframe name
        self.last_executed_script = ""  # Store the last executed script to preserve operations
        self.fixture_code = {
            "imports": ["import pandas as pd", "import numpy as np"],
            "functions": [],
            "data_loading": []
        }  # Store fixture code (imports, functions, data loading)

    def show_manager(self):
        # Create dialog for code manager
        self.manager_dialog = QDialog(self.main_app)
        self.manager_dialog.setWindowTitle("Code Manager")
        self.manager_dialog.setMinimumSize(900, 700)

        # Create layout
        layout = QVBoxLayout()

        # Add view type selection
        view_layout = QHBoxLayout()

        view_label = QLabel("View Mode:")
        view_layout.addWidget(view_label)

        # Create radio buttons for view options
        self.current_df_radio = QRadioButton("Current DataFrame Only")
        self.full_history_radio = QRadioButton("Full Operation History")

        # Group the radio buttons
        self.button_group = QButtonGroup()
        self.button_group.addButton(self.current_df_radio)
        self.button_group.addButton(self.full_history_radio)

        # Set default selection
        self.current_df_radio.setChecked(True)

        # Add to layout
        view_layout.addWidget(self.current_df_radio)
        view_layout.addWidget(self.full_history_radio)
        layout.addLayout(view_layout)

        # Create layout for fixture code (upper) and operations (lower)
        code_layout = QVBoxLayout()

        # Add fixture code section (upper)
        fixture_layout = QVBoxLayout()
        fixture_layout.addWidget(QLabel("Fixture Code (imports, functions, data loading)"))

        # Add text edit for fixture code
        self.fixture_edit = QTextEdit()
        self.fixture_edit.setPlaceholderText("# Fixture code includes:\n# - Import statements\n# - Function definitions\n# - Data loading (pd.read_csv)")
        fixture_layout.addWidget(self.fixture_edit)

        # Add operations section (lower)
        operations_layout = QVBoxLayout()
        operations_layout.addWidget(QLabel("DataFrame Operations"))

        # Add text edit for operations
        self.operations_edit = QTextEdit()
        self.operations_edit.setPlaceholderText("# Operations applied to DataFrames\n# Example: df = df.query('column > 0')")
        operations_layout.addWidget(self.operations_edit)

        # Add both sections to the code layout
        code_layout.addLayout(fixture_layout, 1)  # Upper part gets 1/3 of the space
        code_layout.addLayout(operations_layout, 2)  # Lower part gets 2/3 of the space

        # Add code layout to the main layout
        layout.addLayout(code_layout)

        # Store current dataframe name and initialize script content
        self.current_df_name = None

        # Generate appropriate script based on the current dataframe
        if self.main_app.df_viewer.currentIndex() >= 0:
            df_name = self.main_app.df_viewer.tabText(self.main_app.df_viewer.currentIndex())
            if df_name in self.main_app.df_manager.dataframes:
                # Debug information
                print(f"CodeManager.show_manager: Getting script for dataframe {df_name}")
                pandas_df = self.main_app.df_manager.dataframes[df_name]
                print(f"CodeManager.show_manager: DataFrame {df_name} has {len(pandas_df.operations)} operations")

                # Get operations for the current dataframe
                operations_code = self.main_app.df_manager.generate_script(df_name)
                self.operations_edit.setPlainText(operations_code)

                # Set the fixture code
                fixture_code = self.get_fixture_code()
                self.fixture_edit.setPlainText(fixture_code)

                # Store the current dataframe name for updates
                self.current_df_name = df_name
            else:
                self.operations_edit.setPlainText("")
                self.fixture_edit.setPlainText(self.get_fixture_code())
                self.current_df_name = None
        else:
            # If no specific dataframe is selected but there are dataframes in the app,
            # generate a full script with all operations
            if self.main_app.df_manager.dataframes:
                # Debug information
                print(f"CodeManager: Found {len(self.main_app.df_manager.dataframes)} dataframes")

                # Generate operations code from all dataframes
                operations_code = self.main_app.df_manager.generate_full_script()
                self.operations_edit.setPlainText(operations_code)

                # Set the fixture code
                fixture_code = self.get_fixture_code()
                self.fixture_edit.setPlainText(fixture_code)
            else:
                # Fall back to the last executed script if available
                if self.main_app.df_manager.get_last_executed_script() or self.last_executed_script:
                    # Prefer the manager's last script, fall back to instance variable
                    last_script = self.main_app.df_manager.get_last_executed_script() or self.last_executed_script
                    # Parse the script to separate fixture code and operations
                    operations = self.parse_script(last_script) or []

                    # Set the operations code
                    self.operations_edit.setPlainText("\n".join(operations))

                    # Set the fixture code
                    self.fixture_edit.setPlainText(self.get_fixture_code())
                else:
                    # Initialize with empty operation and default fixture code
                    self.operations_edit.setPlainText("")
                    self.fixture_edit.setPlainText(self.get_fixture_code())

        # Add update handler for the radio buttons
        def update_code_view():
            if self.current_df_radio.isChecked():
                # Show only current dataframe operations
                if self.current_df_name and self.main_app.df_manager.get_dataframe(self.current_df_name):
                    operations_code = self.main_app.df_manager.generate_script(self.current_df_name)
                    self.operations_edit.setPlainText(operations_code)
                else:
                    self.operations_edit.setPlainText("# No dataframe selected")
            else:
                # Show full operation history across all dataframes
                operations_code = self.main_app.df_manager.generate_full_script()
                self.operations_edit.setPlainText(operations_code)

        # Connect radio buttons to update handler
        self.current_df_radio.toggled.connect(update_code_view)

        # Add buttons for editor
        btn_layout = QHBoxLayout()

        load_btn = QPushButton("Load Script")
        load_btn.clicked.connect(self.load_script)
        btn_layout.addWidget(load_btn)

        save_btn = QPushButton("Save Script")
        save_btn.clicked.connect(self.save_script)
        btn_layout.addWidget(save_btn)

        run_btn = QPushButton("Run Script")
        run_btn.clicked.connect(self.run_script)
        btn_layout.addWidget(run_btn)

        # Add close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.on_close_manager)
        btn_layout.addWidget(close_btn)

        layout.addLayout(btn_layout)

        self.manager_dialog.setLayout(layout)
        self.manager_dialog.show()

    def on_close_manager(self):
        # Save the current fixture code and operations before closing
        fixture_code = self.fixture_edit.toPlainText()
        operations_code = self.operations_edit.toPlainText()

        # Update the fixture code from the editor
        self.parse_script(fixture_code)

        # Generate the combined script
        self.current_script = self.generate_combined_script(operations_code)

        # If the current script is the one we've executed, make sure it's saved
        if self.current_script.strip() and (self.current_script != self.last_executed_script):
            # Update last_executed_script if text has changed
            self.last_executed_script = self.current_script

        self.manager_dialog.close()

    def load_script(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self.manager_dialog, "Load Python Script", "", "Python Files (*.py)")

        if file_path:
            try:
                # Store the absolute path of the script file
                self.script_file_path = os.path.abspath(file_path)

                with open(file_path, 'r', encoding='utf-8') as f:
                    self.current_script = f.read()

                # Save the script as the last executed script to make sure it's remembered
                self.last_executed_script = self.current_script
                self.main_app.df_manager.last_executed_script = self.current_script

                # Parse the loaded script to separate fixture code and operations
                operations = self.parse_script(self.current_script) or []

                # Set the fixture code in the upper editor
                self.fixture_edit.setPlainText(self.get_fixture_code())

                # Set the operations code in the lower editor
                self.operations_edit.setPlainText("\n".join(operations))

                # Also force the dataframe manager to extract operations from this script
                # for any existing dataframes
                for df_name in self.main_app.df_manager.dataframes.keys():
                    self.main_app.df_manager._extract_operations_from_script(self.current_script, df_name)

                self.main_app.status_bar.showMessage(f"Script loaded from {file_path}")
            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error loading script: {str(e)}")
                print(f"Script loading error: {str(e)}")

    def save_script(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self.manager_dialog, "Save Python Script",
            os.path.join(os.getcwd(), "script.py"),
            "Python Files (*.py)")

        if file_path:
            try:
                # Get the fixture code and operations
                fixture_code = self.fixture_edit.toPlainText()
                operations_code = self.operations_edit.toPlainText()

                # Update fixture code from the editor
                self.parse_script(fixture_code)

                # Generate the combined script
                self.current_script = self.generate_combined_script(operations_code)

                # Write the combined script to the file with UTF-8 encoding
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_script)

                self.main_app.status_bar.showMessage(f"Script saved to {file_path}")
            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error saving script: {str(e)}")

    def run_script(self):
        """Execute the current script using the DataFrame Manager."""
        # Get fixture code and operations
        fixture_code = self.fixture_edit.toPlainText()
        operations_code = self.operations_edit.toPlainText()

        if not fixture_code.strip() and not operations_code.strip():
            self.main_app.status_bar.showMessage("No code to execute")
            return

        # Update fixture code from the editor
        self.parse_script(fixture_code)

        # Generate the combined script
        combined_code = self.generate_combined_script(operations_code)

        # Save the current script
        self.current_script = combined_code
        self.last_executed_script = combined_code

        try:
            # Execute the code through the DataFrame Manager, passing the script file path if it exists
            result = self.main_app.df_manager.execute_script(combined_code, script_path=self.script_file_path)

            if result['success']:
                # Update GUI for any new or changed dataframes
                self.refresh_dataframe_views()

                if result.get('df_found', False):
                    self.main_app.status_bar.showMessage("Script executed successfully. DataFrames loaded in main window.")
                else:
                    self.main_app.status_bar.showMessage("Script executed successfully, but no DataFrames were created.")
            else:
                # Handle error
                error = result['error']
                error_msg = f"Error: {error['type']} - {error['message']}"
                if error.get('line'):
                    error_msg += f" at line {error['line']}"

                self.main_app.status_bar.showMessage(error_msg)

        except Exception as e:
            import traceback
            error_msg = f"Error executing script: {str(e)}"
            self.main_app.status_bar.showMessage(error_msg)
            print(traceback.format_exc())  # Print full traceback for debugging

    def parse_script(self, script_code):
        """Parse a script to identify fixture code and operations"""
        if not script_code.strip():
            return

        lines = script_code.strip().split('\n')
        imports = []
        functions = []
        data_loading = []
        operations = []

        # State tracking
        in_function = False
        function_def = []
        current_function_indent = 0
        operation_section_started = False

        for line in lines:
            stripped = line.strip()

            # Skip empty lines and comments
            if not stripped or stripped.startswith('#'):
                continue

            # Identify imports
            if stripped.startswith('import ') or stripped.startswith('from '):
                imports.append(line)
                continue

            # Identify function definitions
            if stripped.startswith('def ') and not in_function:
                in_function = True
                function_def = [line]
                current_function_indent = len(line) - len(line.lstrip())
                continue

            # Collect function content
            if in_function:
                function_def.append(line)
                # Check if we've exited the function based on indentation
                if line.strip() and (len(line) - len(line.lstrip()) <= current_function_indent):
                    in_function = False
                    functions.append('\n'.join(function_def))
                    function_def = []
                continue

            # Identify data loading (pd.read_csv or similar)
            if ('pd.read_' in stripped or 'pandas.read_' in stripped) and '=' in stripped:
                data_loading.append(line)
                continue

            # Once we find operations applied to dataframes, mark the operation section as started
            if not operation_section_started and any(op in stripped for op in ['.query', '.filter', '.map', '.apply',
                                                                           '.groupby', '.sort_', '.head', '.tail',
                                                                           '.drop', '.rename', '.fillna', '.replace',
                                                                           '[', '.loc', '.iloc', '.at', '.iat']):
                operation_section_started = True

            # All other code after the imports, functions, and data loading is considered operations
            if operation_section_started:
                operations.append(line)

        # Update fixture code
        if imports:
            self.fixture_code['imports'] = imports
        if functions:
            self.fixture_code['functions'] = functions
        if data_loading:
            self.fixture_code['data_loading'] = data_loading

        print(f"Parsed script: {len(imports)} imports, {len(functions)} functions, {len(data_loading)} data loading statements")
        return operations

    def get_fixture_code(self):
        """Get the complete fixture code as a string"""
        result = []

        # Add imports
        result.extend(self.fixture_code['imports'])
        result.append('')  # Empty line after imports

        # Add functions
        if self.fixture_code['functions']:
            result.extend(self.fixture_code['functions'])
            result.append('')  # Empty line after functions

        # Add data loading
        if self.fixture_code['data_loading']:
            result.append('# Data Loading')
            result.extend(self.fixture_code['data_loading'])
            result.append('')  # Empty line after data loading

        return '\n'.join(result)

    def generate_combined_script(self, operations_code):
        """Generate a complete script with fixture code and operations"""
        fixture_code = self.get_fixture_code()

        result = [
            fixture_code,
            '# DataFrame Operations',
            operations_code
        ]

        return '\n'.join(result)

    def add_fixture_code(self, code_type, code):
        """Add code to the fixture section"""
        if code_type in self.fixture_code:
            if isinstance(code, list):
                self.fixture_code[code_type].extend(code)
            else:
                self.fixture_code[code_type].append(code)

    def refresh_dataframe_views(self):
        """Refresh dataframe views in the UI after script execution"""
        # Check if any dataframes need new tabs
        for df_name in self.main_app.df_manager.dataframes.keys():
            # Check if there's already a tab for this dataframe
            found = False
            for i in range(self.main_app.df_viewer.count()):
                if self.main_app.df_viewer.tabText(i) == df_name:
                    # Tab exists, refresh its model if needed
                    table_view = self.main_app.df_viewer.widget(i)
                    if table_view and hasattr(table_view, 'model'):
                        table_view.model().layoutChanged.emit()
                    found = True
                    break

            # If no tab exists for this dataframe, create one
            if not found:
                self.main_app.df_viewer.add_dataframe_tab(df_name)

    def generate_script(self, df_name):
        """Generate script for a dataframe using the DataFrame Manager"""
        print(f"\n===== CODE MANAGER: generate_script() for {df_name} =====")

        # Get the dataframe object to debug operations directly
        if df_name in self.main_app.df_manager.dataframes:
            df_obj = self.main_app.df_manager.dataframes[df_name]
            print(f"Found dataframe object with {len(df_obj.operations)} operations")

            # Log operations and their sequence numbers
            print("Operations in memory (original order):")
            for i, op in enumerate(df_obj.operations):
                print(f"  {i+1}. [{op.seq_no}] {op.operation_type}: {op.description} - Code: {op.code[:50]}...")

            # Log operations sorted by sequence number
            sorted_ops = sorted(df_obj.operations, key=lambda x: x.seq_no)
            print("\nOperations sorted by sequence number:")
            for i, op in enumerate(sorted_ops):
                print(f"  {i+1}. [{op.seq_no}] {op.operation_type}: {op.description} - Code: {op.code[:50]}...")
        else:
            print(f"Dataframe {df_name} not found in DataFrame Manager")

        # Call the original method
        script = self.main_app.df_manager.generate_script(df_name)

        # Log the generated script
        print(f"\nGenerated script ({len(script.splitlines())} lines):")
        for i, line in enumerate(script.splitlines()[:20]):  # Print first 20 lines
            if i < 10 or "#" in line:  # Focus on comments and first few lines
                print(f"  {i+1}: {line}")
        if len(script.splitlines()) > 20:
            print(f"  ... {len(script.splitlines()) - 20} more lines")

        return script

    def export_script(self, df_name):
        script = self.generate_script(df_name)

        # Open save file dialog
        file_path, _ = QFileDialog.getSaveFileName(
            self.main_app, "Save Python Script",
            os.path.join(os.getcwd(), f"{df_name}_script.py"),
            "Python Files (*.py)")

        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(script)
            self.main_app.status_bar.showMessage(f"Script saved to {file_path}")

    def show_code(self, df_name=None, show_history=False):
        # If no df_name provided, use current tab
        if df_name is None:
            current_index = self.main_app.df_viewer.currentIndex()

            if current_index >= 0:
                df_name = self.main_app.df_viewer.tabText(current_index)
            else:
                self.main_app.status_bar.showMessage("No dataframe selected")
                return

        if not self.main_app.df_manager.get_dataframe(df_name):
            self.main_app.status_bar.showMessage(f"Dataframe {df_name} not found")
            return

        # Create dialog to show code
        code_dialog = QDialog(self.main_app)
        code_dialog.setWindowTitle(f"Code for {df_name}")
        code_dialog.setMinimumSize(700, 500)

        # Create layout
        layout = QVBoxLayout()

        # Add radio buttons for view type
        view_layout = QHBoxLayout()
        from PyQt6.QtWidgets import QRadioButton, QButtonGroup

        current_df_radio = QRadioButton("Current DataFrame Only")
        full_history_radio = QRadioButton("Full Operation History")

        button_group = QButtonGroup()
        button_group.addButton(current_df_radio)
        button_group.addButton(full_history_radio)

        # Set default selection
        if show_history:
            full_history_radio.setChecked(True)
        else:
            current_df_radio.setChecked(True)

        view_layout.addWidget(current_df_radio)
        view_layout.addWidget(full_history_radio)
        layout.addLayout(view_layout)

        # Add text edit for code
        code_edit = QTextEdit()
        code_edit.setReadOnly(True)

        if show_history:
            # Create full history script that shows all operations from all dataframes
            script = self.generate_full_script()
        else:
            # Show only the current dataframe's script
            script = self.generate_script(df_name)

        code_edit.setPlainText(script)
        layout.addWidget(code_edit)

        # Connect radio buttons to update code view
        def update_code_view():
            if current_df_radio.isChecked():
                code_edit.setPlainText(self.generate_script(df_name))
            else:
                code_edit.setPlainText(self.generate_full_script())

        current_df_radio.toggled.connect(update_code_view)

        # Add buttons
        button_layout = QHBoxLayout()

        # Copy button
        copy_btn = QPushButton("Copy to Clipboard")
        copy_btn.clicked.connect(lambda: self.copy_to_clipboard(code_edit.toPlainText()))
        button_layout.addWidget(copy_btn)

        # Export button
        export_btn = QPushButton("Export to File")
        export_btn.clicked.connect(lambda: self.export_script(df_name))
        button_layout.addWidget(export_btn)

        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(code_dialog.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        code_dialog.setLayout(layout)
        code_dialog.show()

    def copy_to_clipboard(self, text):
        clipboard = self.main_app.clipboard()
        clipboard.setText(text)
        self.main_app.status_bar.showMessage("Code copied to clipboard")

    def update_code(self, df_name=None):
        """Update code text in the editors if they exist"""
        # If no df_name provided, use current tab
        if df_name is None and self.current_df_name is not None:
            df_name = self.current_df_name

        if df_name is None:
            # No dataframe to show, generate full operations script
            operations_code = self.main_app.df_manager.generate_full_script()
        else:
            # Generate operations script for specific dataframe
            operations_code = self.main_app.df_manager.generate_script(df_name)

        # Update code display if the editors exist
        if hasattr(self, 'operations_edit') and self.operations_edit:
            self.operations_edit.setPlainText(operations_code)

        if hasattr(self, 'fixture_edit') and self.fixture_edit:
            self.fixture_edit.setPlainText(self.get_fixture_code())

        # Combine for current_script
        self.current_script = self.generate_combined_script(operations_code)

        # Log the update
        if df_name:
            self.main_app.status_bar.showMessage(f"Code updated for {df_name}")
        else:
            self.main_app.status_bar.showMessage("Code updated for all dataframes")

    def generate_full_script(self):
        """Generate a script that includes all operations across all dataframes"""
        return self.main_app.df_manager.generate_full_script()
