import pandas as pd
import numpy as np
from pathlib import Path

# Load source dataframes
df_HK_800700_5min_candles = pd.read_csv(Path(__file__).parent.parent / 'data_files/HK_800700_5min_candles.csv')
# Note: df_HK_800700_5min_candles_query will be created from df_HK_800700_5min_candles through operations
# Note: df_HK_800700_5min_candles_query_1 will be created from df_HK_800700_5min_candles through operations

# Applied operations in sequence:
# [HK_800700_5min_candles] Create/modify column time_date (#1)
df_HK_800700_5min_candles['time_date'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.date
# [HK_800700_5min_candles] Create/modify column time_hour (#2)
df_HK_800700_5min_candles['time_hour'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.hour
# [HK_800700_5min_candles] Create/modify column time_minute (#3)
df_HK_800700_5min_candles['time_minute'] = pd.to_datetime(df_HK_800700_5min_candles['time']).dt.minute
# [HK_800700_5min_candles] Create/modify column time_hour_*60.0 (#4)
df_HK_800700_5min_candles['time_hour_*60.0'] = df_HK_800700_5min_candles['time_hour'] * 60.0
# [HK_800700_5min_candles] Create/modify column time_hour_*60.0_+_time_minute (#5)
df_HK_800700_5min_candles['time_hour_*60.0_+_time_minute'] = df_HK_800700_5min_candles['time_hour_*60.0'] + df_HK_800700_5min_candles['time_minute']
# [HK_800700_5min_candles] Operation from script (#6)
df_HK_800700_5min_candles.rename(columns={'time_hour_*60.0_+_time_minute': 'minute_no'}, inplace=True)
# [HK_800700_5min_candles_query] Query: (minute_no == 575) (#7)
df_HK_800700_5min_candles_query = df_HK_800700_5min_candles.query('minute_no == 575').copy()
# [HK_800700_5min_candles_query_1] Query: (minute_no == 630) (#8)
df_HK_800700_5min_candles_query_1 = df_HK_800700_5min_candles.query('minute_no == 630').copy()