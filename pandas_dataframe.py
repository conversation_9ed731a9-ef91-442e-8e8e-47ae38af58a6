import pandas as pd
import os
from operation import Operation

class PandasDataFrame:
    """
    A wrapper class for pandas DataFrame with additional metadata and operations tracking.
    """
    def __init__(self, data, name, source_path="unknown", add_load_operation=True):
        """
        Initialize a PandasDataFrame object.

        Args:
            data (pd.DataFrame): The pandas DataFrame to wrap
            name (str): Name identifier for this dataframe
            source_path (str): Path to the source file or description of origin
            add_load_operation (bool): Whether to automatically add a load operation
        """
        self.data = data
        self.name = name
        self.source_path = source_path
        self.operations = []  # List of Operation objects
        self.parent = None
        self.hidden_columns = []  # List to track hidden column indices

        # Add a default data loading operation if there's a source path and add_load_operation is True
        if add_load_operation and source_path and source_path != "unknown" and source_path != "script":
            self.add_initial_load_operation(source_path)

    def add_initial_load_operation(self, source_path):
        """
        Add an initial data loading operation for this dataframe.

        Args:
            source_path (str): Path to the source file or description of origin
        """
        # Only add if there are no operations yet
        if not self.operations:
            var_name = f"df_{self.name.replace('-', '_')}"

            # Determine file type and create appropriate loading code
            if source_path.lower().endswith('.csv'):
                code = f"{var_name} = pd.read_csv('{source_path}')"
                op_type = "file_import"
                description = f"Import CSV from {os.path.basename(source_path)}"
            elif source_path.lower().endswith('.xlsx') or source_path.lower().endswith('.xls'):
                code = f"{var_name} = pd.read_excel('{source_path}')"
                op_type = "file_import"
                description = f"Import Excel from {os.path.basename(source_path)}"
            elif source_path.lower().endswith('.json'):
                code = f"{var_name} = pd.read_json('{source_path}')"
                op_type = "file_import"
                description = f"Import JSON from {os.path.basename(source_path)}"
            else:
                code = f"# {var_name} loaded from {source_path}"
                op_type = "file_import"
                description = f"Import data from {os.path.basename(source_path)}"

            # Add the operation
            self.add_operation(
                code=code,
                seq_no=1,  # First operation
                operation_type=op_type,
                description=description
            )

    def add_operation(self, code, seq_no=None, operation_type=None, description=None, main_app=None):
        """
        Add an operation to the history.

        Args:
            code (str): Python code representing the operation
            seq_no (int, optional): Sequence number for the operation
            operation_type (str, optional): Type of operation
            description (str, optional): Human-readable description
            main_app (PandasDesktopApp, optional): Main application instance for global sequence numbers
        """
        # Create source and target dataframe names
        source_df = self.parent if self.parent else self.name
        target_df = self.name

        # Use provided sequence number, get from main app, or generate one locally
        if seq_no is None:
            if main_app is not None:
                # Get a global sequence number from the main app
                seq_no = main_app.get_next_sequence_number()
            else:
                # Fallback if main_app not provided (should be rare)
                seq_no = len(self.operations) + 1

        # Create description if not provided
        if not description:
            if operation_type == "query":
                description = f"Query on {source_df}"
            elif operation_type == "column_add":
                description = f"Add column to {target_df}"
            elif operation_type == "column_rename":
                description = f"Rename column in {target_df}"
            else:
                description = f"Operation on {target_df}"

        # Replace generic 'df' in code with specific variable name if needed
        var_name = f"df_{target_df.replace('-', '_')}"

        # Only do the replacement if the code starts with generic df references
        # and it's not already using a specific variable name
        if code.startswith("df.") or code.startswith("df["):
            code = code.replace("df", var_name, 1)  # Replace only the first occurrence

        # Create and add the operation
        operation = Operation(
            code=code,
            seq_no=seq_no,
            source_df=source_df,
            target_df=target_df,
            description=description,
            operation_type=operation_type
        )

        # Debug information
        print(f"Adding operation to {self.name}: #{seq_no} - {description} - {code}")

        self.operations.append(operation)

    def generate_script(self):
        """
        Generate a Python script that reproduces this dataframe.
        This includes both fixture code and operations.

        Returns:
            str: Python code to recreate the dataframe
        """
        script = [
            "import pandas as pd",
            f"# Load original data from {self.source_path}",
            f"df = pd.read_csv('{self.source_path}')",
            "",
            "# Applied operations:"
        ]

        # Add operations, ensuring numpy import is included if needed
        has_numpy_op = any("np." in op.code for op in self.operations)
        if has_numpy_op and not any("import numpy as np" in op.code for op in self.operations):
            script.insert(1, "import numpy as np")

        # Sort operations by sequence number
        sorted_operations = sorted(self.operations, key=lambda x: x.seq_no)

        # Debug information
        print(f"Generating script for {self.name} with {len(sorted_operations)} operations")
        for op in sorted_operations:
            print(f"Operation #{op.seq_no}: {op.description} - {op.code}")

        for op in sorted_operations:
            # Add a comment with the operation description
            script.append(f"# {op.description} (#{op.seq_no})")

            # Handle query operations specially to ensure they work in the script context
            if op.operation_type == "query" and op.code.startswith("df = df.query"):
                script.append(op.code)
            else:
                script.append(op.code)

        return "\n".join(script)

    def generate_operations_code(self):
        """
        Generate only the operations part of the script without fixture code.

        Returns:
            str: Python code with just the operations
        """
        script = []

        # Sort operations by sequence number
        sorted_operations = sorted(self.operations, key=lambda x: x.seq_no)

        # Debug information
        print(f"Generating operations code for {self.name} with {len(sorted_operations)} operations")

        for op in sorted_operations:
            # Add a comment with the operation description
            script.append(f"# {op.description} (#{op.seq_no})")

            # Handle query operations specially to ensure they work in the script context
            if op.operation_type == "query" and op.code.startswith("df = df.query"):
                script.append(op.code)
            else:
                script.append(op.code)

            script.append("")  # Add empty line for readability

        return "\n".join(script)

    def rename_column(self, old_name, new_name, main_app=None):
        """
        Rename a column in the dataframe.

        Args:
            old_name (str): Current column name
            new_name (str): New column name
            main_app (PandasDesktopApp, optional): Reference to main app for global sequence numbers
        """
        self.data.rename(columns={old_name: new_name}, inplace=True)

        # Use specific variable name instead of generic 'df'
        var_name = f"df_{self.name.replace('-', '_')}"
        operation_code = f"{var_name}.rename(columns={{'{old_name}': '{new_name}'}}, inplace=True)"
        self.add_operation(
            code=operation_code,
            operation_type="column_rename",
            description=f"Rename column '{old_name}' to '{new_name}'",
            main_app=main_app
        )

    def add_column(self, name, values, source_column=None, main_app=None):
        """
        Add a new column to the dataframe.

        Args:
            name (str): Name for the new column
            values (array-like): Values for the column
            source_column (str, optional): Name of source column if copied
            main_app (PandasDesktopApp, optional): Reference to main app for global sequence numbers
        """
        self.data[name] = values

        # Use specific variable name instead of generic 'df'
        var_name = f"df_{self.name.replace('-', '_')}"

        if source_column:
            operation_code = f"{var_name}['{name}'] = {var_name}['{source_column}'].copy()"
            description = f"Copy column '{source_column}' to '{name}'"
        else:
            operation_code = f"# Column '{name}' added manually to {var_name}"
            description = f"Add column '{name}' manually"

        self.add_operation(
            code=operation_code,
            operation_type="column_add",
            description=description,
            main_app=main_app
        )

    def query(self, query_str, main_app=None):
        """
        Execute a query and return a new PandasDataFrame with the result.

        Args:
            query_str (str): Query string to execute
            main_app (PandasDesktopApp, optional): Reference to main app for global sequence numbers

        Returns:
            PandasDataFrame: New dataframe with query results
        """
        result = self.data.query(query_str)
        result_df = PandasDataFrame(
            data=result,
            name=f"{self.name}_query_result",
            source_path=self.source_path
        )
        result_df.parent = self.name

        # Add operation to history with specific variable names
        source_var = f"df_{self.name.replace('-', '_')}"
        result_var = f"df_{result_df.name.replace('-', '_')}"

        operation_code = f"{result_var} = {source_var}.query('{query_str}')"

        self.add_operation(
            code=operation_code,
            operation_type="query",
            description=f"Query: {query_str}",
            main_app=main_app
        )

        return result_df

    def hide_columns(self, column_indices):
        """
        Hide the specified columns from the view.

        Args:
            column_indices (list): List of column indices to hide
        """
        if not column_indices:
            return

        # Add the indices to the hidden columns list if they're not already there
        for col_idx in column_indices:
            if col_idx not in self.hidden_columns and col_idx < len(self.data.columns):
                self.hidden_columns.append(col_idx)

        # Sort the hidden columns list for efficient mapping
        self.hidden_columns.sort()

        print(f"Columns {column_indices} hidden in {self.name}. Total hidden columns: {len(self.hidden_columns)}")

    def show_all_columns(self):
        """
        Make all hidden columns visible again.
        """
        if not self.hidden_columns:
            return

        # Clear the hidden columns list
        hidden_count = len(self.hidden_columns)
        self.hidden_columns = []

        print(f"All {hidden_count} hidden columns are now visible in {self.name}")

    def get_hidden_columns_count(self):
        """
        Get the number of currently hidden columns.

        Returns:
            The number of hidden columns
        """
        return len(self.hidden_columns)
