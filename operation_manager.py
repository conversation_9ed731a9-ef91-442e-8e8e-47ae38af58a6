from PyQt6.QtWidgets import (QMenu, QInputDialog, QApplication, QDialog, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QListWidget, QPushButton,
                            QCheckBox, QDialogButtonBox, QComboBox, QMessageBox, QFormLayout,
                            QListWidgetItem, QSpinBox, QTabWidget, QWidget, QTextEdit, QFrame,
                            QGridLayout)
from PyQt6.QtGui import QAction
from PyQt6.QtCore import Qt, QMimeData
from ui.dialogs.query_dialog import QueryDialog
from ui.dialogs.chart_dialog import ChartDialog
from ui.dialogs.merge_dialog import MergeDialog
from ui.dialogs.shift_operation_dialog import ShiftOperationDialog
from ui.dialogs.diff_operation_dialog import DiffOperationDialog
from ui.dialogs.multi_shift_operation_dialog import MultiShiftOperationDialog
from ui.dialogs.multi_diff_operation_dialog import MultiDiffOperationDialog
from ui.dialogs.rename_column_dialog import RenameColumnDialog
from ui.dialogs.rolling_operation_dialog import RollingOperationDialog
from ui.dialogs.groupby_transform_dialog import GroupbyTransformDialog
from ui.dialogs.arithmetic_operation_dialog import ArithmeticOperationDialog
from ui.dialogs.info_dialog import InfoDialog
import pandas as pd
import numpy as np
import json
import os
from pandas_dataframe import PandasDataFrame
from python_interpreter import PythonInterpreter
from query import Query


class OperationManager:
    def __init__(self, main_app):
        self.main_app = main_app
        self.copied_column = None
        self.copied_column_name = None
        self.saved_queries = []  # List of Query objects
        self.interpreter = PythonInterpreter()  # Create a Python interpreter instance
        self.load_saved_queries()

    def _get_actual_column_index(self, df_name, visible_column_index):
        """
        Maps a visible column index (what the user sees in the UI) to the actual column index in the dataframe.
        This accounts for hidden columns.

        Args:
            df_name: The dataframe name
            visible_column_index: The column index in the UI (visible to the user)

        Returns:
            The actual column index in the dataframe
        """
        # Get the current table view and model
        current_tab = self.main_app.df_viewer.currentWidget()
        model = current_tab.model()

        # If the model has a method to map column indices, use it
        if hasattr(model, '_map_to_actual_column'):
            return model._map_to_actual_column(visible_column_index)

        # Otherwise, return the visible index as is (no hidden columns)
        return visible_column_index

    def load_saved_queries(self):
        """Load saved queries from a JSON file"""
        queries_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "saved_queries.json")
        if os.path.exists(queries_file):
            try:
                with open(queries_file, 'r', encoding='utf-8') as f:
                    loaded_queries = json.load(f)

                    # Convert loaded data to Query objects
                    self.saved_queries = []
                    if loaded_queries and isinstance(loaded_queries, list):
                        for query_data in loaded_queries:
                            # Handle old dict format
                            if isinstance(query_data, dict) and 'name' in query_data and 'criteria' in query_data:
                                self.saved_queries.append(
                                    Query(
                                        name=query_data['name'],
                                        criteria=query_data['criteria'],
                                        description=query_data.get('description', '')
                                    )
                                )
            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error loading saved queries: {str(e)}")

    def save_queries(self):
        """Save queries to a JSON file"""
        queries_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "saved_queries.json")
        try:
            # Convert Query objects to dictionaries for serialization
            query_dicts = [query.to_dict() for query in self.saved_queries]

            with open(queries_file, 'w', encoding='utf-8') as f:
                json.dump(query_dicts, f)
        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error saving queries: {str(e)}")

    def execute_operation_code(self, code, initial_namespace=None, df_name=None):
        """
        Execute operation code using the PythonInterpreter.

        Args:
            code: The Python code string to execute
            initial_namespace: Optional dictionary of variables to include in the namespace
            df_name: The name of the dataframe being operated on

        Returns:
            Dictionary with execution results
        """
        try:
            # Initialize the interpreter's namespace with standard libraries
            # and any provided initial namespace values
            self.interpreter.reset()

            if initial_namespace is not None:
                for key, value in initial_namespace.items():
                    self.interpreter.namespace[key] = value

            # Execute the operation code
            result = self.interpreter.execute(code)

            # Check for success and extract dataframes from the interpreter's namespace
            if result['success']:
                # Find all pandas DataFrames in the namespace
                dataframes = {}
                for var_name, var_value in self.interpreter.namespace.items():
                    if isinstance(var_value, pd.DataFrame):
                        dataframes[var_name] = var_value

                # Add dataframes to the result
                result['dataframes'] = dataframes

            return result
        except Exception as e:
            # Return error information if execution fails
            return {
                'success': False,
                'error': {
                    'type': type(e).__name__,
                    'message': str(e)
                }
            }

    def show_context_menu(self, position, df_name):
        menu = QMenu()

        # Get current table view and selected indexes
        current_tab = self.main_app.df_viewer.currentWidget()
        selected_indexes = current_tab.selectedIndexes()

        # Add common operations
        query_action = QAction("Query", self.main_app)
        query_action.triggered.connect(lambda: self.execute_query(df_name))
        menu.addAction(query_action)

        rename_action = QAction("Rename Column", self.main_app)
        rename_action.triggered.connect(lambda: self.rename_column(df_name))

        # Add info action
        info_action = QAction("Info", self.main_app)
        info_action.triggered.connect(lambda: self.show_dataframe_info(df_name))
        menu.addAction(info_action)

        # Add chart action
        chart_action = QAction("Chart", self.main_app)
        # If a single column is selected, pass its index to create_chart
        if selected_indexes:
            columns = list(set(index.column() for index in selected_indexes))
            if len(columns) == 1:
                chart_action.triggered.connect(lambda: self.create_chart(df_name, columns[0]))
            else:
                chart_action.triggered.connect(lambda: self.create_chart(df_name, None))
        else:
            chart_action.triggered.connect(lambda: self.create_chart(df_name, None))
        menu.addAction(chart_action)

        # Add copy column action if a column is selected
        if selected_indexes:
            # Get unique columns
            columns = sorted(set(index.column() for index in selected_indexes))

            # Copy column action only available for single column selection
            if len(columns) == 1:
                copy_action = QAction("Copy Column", self.main_app)
                copy_action.triggered.connect(lambda: self.copy_column(df_name, columns[0]))
                menu.addAction(copy_action)

            # Add shift action - supports multiple columns
            shift_action = QAction("Shift", self.main_app)
            shift_action.triggered.connect(lambda: self.shift_column(df_name, columns))
            menu.addAction(shift_action)

            # Add diff action - supports multiple columns
            diff_action = QAction("Diff", self.main_app)
            diff_action.triggered.connect(lambda: self.diff_column(df_name, columns))
            menu.addAction(diff_action)

            # Add max action - supports multiple columns
            max_action = QAction("Max", self.main_app)
            max_action.triggered.connect(lambda: self.max_operation(df_name, columns))
            menu.addAction(max_action)

        # Add paste column action if a column has been copied
        if self.copied_column is not None:
            paste_action = QAction("Paste Column", self.main_app)
            paste_action.triggered.connect(lambda: self.paste_column(df_name))
            menu.addAction(paste_action)

        # Add datetime operations submenu
        if selected_indexes:
            columns = set(index.column() for index in selected_indexes)
            if len(columns) == 1:
                dt_menu = QMenu("dt", menu)

                date_action = QAction("date", self.main_app)
                date_action.triggered.connect(lambda: self.dt_date(df_name, list(columns)[0]))
                dt_menu.addAction(date_action)

                month_action = QAction("month", self.main_app)
                month_action.triggered.connect(lambda: self.dt_month(df_name, list(columns)[0]))
                dt_menu.addAction(month_action)

                day_action = QAction("day", self.main_app)
                day_action.triggered.connect(lambda: self.dt_day(df_name, list(columns)[0]))
                dt_menu.addAction(day_action)

                hour_action = QAction("hour", self.main_app)
                hour_action.triggered.connect(lambda: self.dt_hour(df_name, list(columns)[0]))
                dt_menu.addAction(hour_action)

                minute_action = QAction("minute", self.main_app)
                minute_action.triggered.connect(lambda: self.dt_minute(df_name, list(columns)[0]))
                dt_menu.addAction(minute_action)

                menu.addMenu(dt_menu)

        # Add numpy operations submenu
        if selected_indexes:
            columns = sorted(set(index.column() for index in selected_indexes))
            # Allow numpy operations for any number of columns
            numpy_menu = QMenu("numpy", menu)

            log_action = QAction("log", self.main_app)
            log_action.triggered.connect(lambda: self.numpy_log(df_name, columns))
            numpy_menu.addAction(log_action)

            exp_action = QAction("exp", self.main_app)
            exp_action.triggered.connect(lambda: self.numpy_exp(df_name, columns))
            numpy_menu.addAction(exp_action)

            sign_action = QAction("sign", self.main_app)
            sign_action.triggered.connect(lambda: self.numpy_sign(df_name, columns))
            numpy_menu.addAction(sign_action)

            menu.addMenu(numpy_menu)

            # Add rolling operations submenu - only for single column selection
            if len(columns) == 1:
                rolling_menu = QMenu("Rolling", menu)

                rolling_mean_action = QAction("Mean", self.main_app)
                rolling_mean_action.triggered.connect(lambda: self.rolling_mean(df_name, columns[0]))
                rolling_menu.addAction(rolling_mean_action)

                rolling_median_action = QAction("Median", self.main_app)
                rolling_median_action.triggered.connect(lambda: self.rolling_median(df_name, columns[0]))
                rolling_menu.addAction(rolling_median_action)

                rolling_max_action = QAction("Max", self.main_app)
                rolling_max_action.triggered.connect(lambda: self.rolling_max(df_name, columns[0]))
                rolling_menu.addAction(rolling_max_action)

                rolling_min_action = QAction("Min", self.main_app)
                rolling_min_action.triggered.connect(lambda: self.rolling_min(df_name, columns[0]))
                rolling_menu.addAction(rolling_min_action)

                menu.addMenu(rolling_menu)

                # Add groupby operations submenu
                groupby_menu = QMenu("groupby", menu)

                transform_action = QAction("transform", self.main_app)
                transform_action.triggered.connect(lambda: self.groupby_transform(df_name, columns[0]))
                groupby_menu.addAction(transform_action)

                menu.addMenu(groupby_menu)

        # Add arithmetic operations submenu - only for single column selection
        if selected_indexes:
            # We need to redefine columns here since it might have been modified above
            arithmetic_columns = sorted(set(index.column() for index in selected_indexes))
            if len(arithmetic_columns) == 1:
                arithmetic_menu = QMenu("Arithmetic", menu)

                add_action = QAction("+", self.main_app)
                add_action.triggered.connect(lambda: self.arithmetic_operation(df_name, arithmetic_columns[0], "+"))
                arithmetic_menu.addAction(add_action)

                subtract_action = QAction("-", self.main_app)
                subtract_action.triggered.connect(lambda: self.arithmetic_operation(df_name, arithmetic_columns[0], "-"))
                arithmetic_menu.addAction(subtract_action)

                multiply_action = QAction("*", self.main_app)
                multiply_action.triggered.connect(lambda: self.arithmetic_operation(df_name, arithmetic_columns[0], "*"))
                arithmetic_menu.addAction(multiply_action)

                divide_action = QAction("/", self.main_app)
                divide_action.triggered.connect(lambda: self.arithmetic_operation(df_name, arithmetic_columns[0], "/"))
                arithmetic_menu.addAction(divide_action)

                menu.addMenu(arithmetic_menu)

        # Add drop menu for columns and rows
        if selected_indexes:
            # We need to ensure we have a fresh set of selected indexes
            drop_menu = QMenu("Drop", menu)

            drop_column_action = QAction("Column", self.main_app)
            drop_column_action.triggered.connect(lambda: self.drop_columns(df_name))
            drop_menu.addAction(drop_column_action)

            drop_row_action = QAction("Row", self.main_app)
            drop_row_action.triggered.connect(lambda: self.drop_rows(df_name))
            drop_menu.addAction(drop_row_action)

            menu.addMenu(drop_menu)

            # Add column visibility actions
            hide_columns_action = QAction("Hide Selected Columns", self.main_app)
            hide_columns_action.triggered.connect(lambda: self.hide_columns(df_name))
            menu.addAction(hide_columns_action)

        # Add merge operation
        merge_action = QAction("Merge", self.main_app)
        merge_action.triggered.connect(lambda: self.merge_dataframes(df_name))
        menu.addAction(merge_action)

        # Add show all hidden columns action
        # This action is always available, regardless of selection
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)
        if pandas_df and pandas_df.get_hidden_columns_count() > 0:
            show_all_columns_action = QAction("Show All Hidden Columns", self.main_app)
            show_all_columns_action.triggered.connect(lambda: self.show_all_columns(df_name))
            menu.addAction(show_all_columns_action)

    # Add more operations...
        menu.addAction(rename_action)

        # Show the menu
        menu.exec(current_tab.mapToGlobal(position))

    def copy_column(self, df_name, visible_column_index):
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)

        # Map the visible column index to the actual column index
        actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
        column_name = pandas_df.data.columns[actual_column_index]

        # Store the column data and name
        self.copied_column = pandas_df.data[column_name].copy()
        self.copied_column_name = column_name

        # Also copy to system clipboard as text
        clipboard = QApplication.clipboard()
        mime_data = QMimeData()
        mime_data.setText(self.copied_column.to_string())
        clipboard.setMimeData(mime_data)

        # Update status
        self.main_app.status_bar.showMessage(f"Column '{column_name}' copied")

    def paste_column(self, df_name):
        if self.copied_column is None or not self.copied_column_name:
            self.main_app.status_bar.showMessage("No column available to paste")
            return

        # Get the target dataframe from the manager
        target_df = self.main_app.df_manager.get_dataframe(df_name)
        if not target_df:
            self.main_app.status_bar.showMessage(f"DataFrame {df_name} not found")
            return

        new_column_name = self.copied_column_name

        # Check if column name already exists and add suffix if needed
        suffix = 0
        while new_column_name in target_df.data.columns:
            suffix += 1
            new_column_name = f"{self.copied_column_name}_{suffix}"

        # Add the column to the dataframe
        target_df.data[new_column_name] = self.copied_column.copy()

        # Generate code for this operation
        operation_code = f"df['{new_column_name}'] = df['{self.copied_column_name}'].copy()"

        # Add the operation to the dataframe's history
        target_df.add_operation(
            code=operation_code,
            operation_type="paste",
            description=f"Pasted column '{self.copied_column_name}' as '{new_column_name}'",
            main_app=self.main_app
        )

        # Update the code manager
        self.main_app.code_manager.update_code(df_name)

        # Update the view
        current_tab = self.main_app.df_viewer.currentWidget()
        current_tab.model().layoutChanged.emit()

        # Update status
        self.main_app.status_bar.showMessage(f"Column '{self.copied_column_name}' pasted as '{new_column_name}'")

    def execute_query(self, df_name):
        # Create and show the query dialog
        dialog = QueryDialog(self.main_app, self.saved_queries)

        # Set default result dataframe name
        base_name = f"{df_name}_query"
        result_name = base_name
        counter = 1

        # Ensure the default name is unique
        while self.main_app.df_manager.get_dataframe(result_name):
            result_name = f"{base_name}_{counter}"
            counter += 1

        # Set the default name in the dialog
        dialog.result_name_edit.setText(result_name)

        # Show the dialog
        result = dialog.exec()

        # Update saved queries
        self.saved_queries = dialog.saved_queries
        self.save_queries()

        if result == QDialog.DialogCode.Accepted and dialog.query_text:
            try:
                pandas_df = self.main_app.df_manager.get_dataframe(df_name)
                if pandas_df is None:
                    self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                    return

                # Split queries by line and apply them sequentially
                queries = [q.strip() for q in dialog.query_text.split('\n') if q.strip()]

                if not queries:
                    return

                # Get the user-provided name for the result dataframe
                result_name = dialog.result_df_name.strip()

                # If user didn't provide a name, use the default naming scheme
                if not result_name:
                    base_name = f"{df_name}_query"
                    result_name = base_name
                    counter = 1

                    # Ensure the name is unique
                    while self.main_app.df_manager.get_dataframe(result_name):
                        result_name = f"{base_name}_{counter}"
                        counter += 1
                else:
                    # If user provided a name, still ensure it's unique by adding a suffix if needed
                    original_name = result_name
                    counter = 1

                    while self.main_app.df_manager.get_dataframe(result_name):
                        result_name = f"{original_name}_{counter}"
                        counter += 1

                # Use variable names based on the dataframe names
                source_var = df_name.replace('-', '_')
                target_var = result_name.replace('-', '_')

                # Generate code with proper variable names
                query_code = f"{target_var} = {source_var}" + "".join([f".query('{q}')" for q in queries]) + ".copy()"

                # Create initial namespace with the dataframe
                namespace = {source_var: pandas_df.data.copy()}

                # Execute the operation through the Python interpreter
                execution_result = self.execute_operation_code(query_code, namespace, df_name)

                if execution_result['success'] is not True:
                    # Handle execution error
                    error = execution_result['error']
                    self.main_app.status_bar.showMessage(f"Error executing query: {error['message']}")
                    return

                # Check if the expected dataframe was created
                if target_var not in execution_result['dataframes']:
                    self.main_app.status_bar.showMessage(f"Error: Query result dataframe '{target_var}' not found")
                    return

                # Get the resulting dataframe
                result_data = execution_result['dataframes'][target_var]

                # Create a description for the query operation
                query_description = f"Query: " + " AND ".join([f"({q})" for q in queries])

                # Add the dataframe to the dataframe manager instead of directly to main_app.dataframes
                self.main_app.df_manager.add_dataframe(
                    data=result_data,
                    name=result_name,
                    source_path=pandas_df.source_path,
                    parent=df_name
                )

                # Add the query operation to the dataframe manager
                self.main_app.df_manager.add_operation(
                    df_name=result_name,
                    code=query_code,
                    operation_type="query",
                    description=query_description
                )

                # Update the code manager
                self.main_app.code_manager.update_code(result_name)

                # Show result
                self.main_app.df_viewer.add_dataframe_tab(result_name)

                # Update status
                self.main_app.status_bar.showMessage(f"Applied {len(queries)} queries to create '{result_name}'")

                # Update the view
                current_tab = self.main_app.df_viewer.currentWidget()
                current_tab.model().layoutChanged.emit()

            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error executing query: {str(e)}")
                import traceback
                traceback.print_exc()

    def rename_column(self, df_name):
        """
        Rename a column in the dataframe.

        Args:
            df_name: The dataframe name
        """
        try:
            # Get current table view and selected indexes
            current_tab = self.main_app.df_viewer.currentWidget()
            selected_indexes = current_tab.selectedIndexes()

            if not selected_indexes:
                return

            # Get the visible column index
            visible_column_index = selected_indexes[0].column()
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)

            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Map the visible column index to the actual column index
            actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
            old_name = pandas_df.data.columns[actual_column_index]

            # Create and show the rename column dialog
            dialog = RenameColumnDialog(self.main_app, old_name)
            result = dialog.exec()

            if result == QDialog.DialogCode.Accepted:
                new_name = dialog.new_name

                # If the name hasn't changed, no need to do anything
                if new_name == old_name:
                    return

                try:
                    # Use proper dataframe variable name instead of generic 'df'
                    var_name = df_name.replace('-', '_')

                    # Create operation code with proper variable name
                    operation_code = f"{var_name}.rename(columns={{'{old_name}': '{new_name}'}}, inplace=True)"

                    # Create initial namespace with the dataframe
                    namespace = {var_name: pandas_df.data.copy()}

                    # Execute the operation through the Python interpreter
                    execution_result = self.execute_operation_code(operation_code, namespace, df_name)

                    if execution_result['success'] is not True:
                        # Handle execution error
                        error = execution_result['error']
                        self.main_app.status_bar.showMessage(f"Error renaming column: {error['message']}")
                        return

                    # Update the dataframe with the result from the interpreter
                    if var_name in execution_result['dataframes']:
                        # Update the dataframe data
                        pandas_df.data = execution_result['dataframes'][var_name]

                        # Add the operation to the dataframe's history
                        pandas_df.add_operation(
                            code=operation_code,
                            operation_type="rename",
                            description=f"Renamed column '{old_name}' to '{new_name}'",
                            main_app=self.main_app
                        )

                        # Update the code manager
                        self.main_app.code_manager.update_code(df_name)

                        # Update the model with the new data to ensure column names are refreshed
                        current_tab.model().update_data(pandas_df.data)

                        # Update status
                        self.main_app.status_bar.showMessage(f"Column renamed from '{old_name}' to '{new_name}'")
                    else:
                        self.main_app.status_bar.showMessage(f"Error: DataFrame not found in execution result")
                except Exception as e:
                    self.main_app.status_bar.showMessage(f"Error renaming column: {str(e)}")

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error renaming column: {str(e)}")

    def dt_component(self, df_name, visible_column_index, component):
        """
        Extract a datetime component from a column.

        Args:
            df_name: The dataframe name
            visible_column_index: The visible column index in the UI
            component: The datetime component to extract (e.g., 'date', 'month', 'day', 'hour', etc.)
        """
        try:
            # Get dataframe and column information
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Map the visible column index to the actual column index
            actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
            column_name = pandas_df.data.columns[actual_column_index]

            # Create new column name with appropriate suffix
            new_column_name = f"{column_name}_{component}"

            # Use proper dataframe variable name instead of generic 'df'
            var_name = df_name.replace('-', '_')

            # Create initial namespace with the dataframe
            namespace = {var_name: pandas_df.data.copy()}

            # Create operation code for the specified component
            operation_code = f"{var_name}['{new_column_name}'] = pd.to_datetime({var_name}['{column_name}']).dt.{component}"

            # Execute the operation using the PythonInterpreter
            execution_result = self.execute_operation_code(operation_code, namespace, df_name)

            if execution_result['success'] is not True:
                # Handle execution error
                error = execution_result['error']
                self.main_app.status_bar.showMessage(f"Error creating {component} column: {error['message']}")
                return

            # Update the dataframe with the result from the interpreter
            if var_name in execution_result['dataframes']:
                # Update the dataframe data
                pandas_df.data = execution_result['dataframes'][var_name]

                # Add the operation to the dataframe's history
                pandas_df.add_operation(
                    code=operation_code,
                    operation_type="datetime",
                    description=f"Extract {component} from '{column_name}'",
                    main_app=self.main_app
                )

                # Update the code manager
                self.main_app.code_manager.update_code(df_name)

                # Update the model with the new data to ensure column names are refreshed
                current_tab = self.main_app.df_viewer.currentWidget()
                current_tab.model().update_data(pandas_df.data)

                # Update status
                self.main_app.status_bar.showMessage(f"Added {component} column '{new_column_name}'")
            else:
                self.main_app.status_bar.showMessage(f"Error: DataFrame not found in execution result")
        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error creating {component} column: {str(e)}")

    def dt_date(self, df_name, visible_column_index):
        """
        Extract date from a datetime column.
        """
        # current_tab = self.main_app.df_viewer.currentWidget()
        # selected_indexes = current_tab.selectedIndexes()

        # if not selected_indexes:
        #     return

        # # Get the column to process (overrides the input column_index)
        # column_index = selected_indexes[0].column()
        self.dt_component(df_name, visible_column_index, 'date')

    def dt_month(self, df_name, visible_column_index):
        """
        Extract month from a datetime column.
        """
        self.dt_component(df_name, visible_column_index, 'month')

    def dt_day(self, df_name, visible_column_index):
        """
        Extract day from a datetime column.
        """
        self.dt_component(df_name, visible_column_index, 'day')

    def dt_hour(self, df_name, visible_column_index):
        """
        Extract hour from a datetime column.
        """
        self.dt_component(df_name, visible_column_index, 'hour')

    def dt_minute(self, df_name, visible_column_index):
        """
        Extract minute from a datetime column.
        """
        self.dt_component(df_name, visible_column_index, 'minute')

    def apply_numpy_operation(self, df_name, visible_column_indices, operation_type):
        """
        Apply a numpy operation to one or more columns and create new columns with the results.

        Args:
            df_name: The dataframe name
            visible_column_indices: A list of visible column indices in the UI
            operation_type: The numpy operation to apply (e.g., 'log', 'exp')
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)
        if pandas_df is None:
            self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
            return

        # Ensure visible_column_indices is a list
        if not isinstance(visible_column_indices, list):
            visible_column_indices = [visible_column_indices]

        # Operation-specific configurations
        operation_configs = {
            'log': {
                'function': 'np.log',
                'suffix': 'log',
                'description': 'natural log',
                'warning_check': lambda col: (col <= 0).any(),
                'warning_message': "Warning: Log applied only to positive values. Negative and zero values will result in NaN."
            },
            'exp': {
                'function': 'np.exp',
                'suffix': 'exp',
                'description': 'exponential',
                'warning_check': None,  # No specific warning for exp operation
                'warning_message': None
            },
            'sign': {
                'function': 'np.sign',
                'suffix': 'sign',
                'description': 'sign',
                'warning_check': None,  # No specific warning for sign operation
                'warning_message': None
            }
        }

        # Get the configuration for the specified operation
        if operation_type not in operation_configs:
            self.main_app.status_bar.showMessage(f"Error: Unsupported operation '{operation_type}'")
            return

        config = operation_configs[operation_type]

        try:
            # Use proper dataframe variable name instead of generic 'df'
            # Create a valid Python variable name from the dataframe name
            var_name = df_name.replace('-', '_')

            # Prepare the initial namespace with the actual dataframe
            initial_namespace = {var_name: pandas_df.data.copy()}

            # Build operation code for all selected columns
            operation_code_lines = []
            column_names = []

            for visible_column_index in visible_column_indices:
                # Map the visible column index to the actual column index
                actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
                column_name = pandas_df.data.columns[actual_column_index]
                column_names.append(column_name)

                # Check for any operation-specific warnings for this column
                if config['warning_check'] is not None:
                    numeric_column = pd.to_numeric(pandas_df.data[column_name], errors='coerce')
                    if config['warning_check'](numeric_column):
                        self.main_app.status_bar.showMessage(f"{config['warning_message']} (Column: {column_name})")

                # Define the new column name
                new_column_name = f"{column_name}_{config['suffix']}"

                # Add operation code for this column
                operation_code_lines.append(
                    f"{var_name}['{new_column_name}'] = {config['function']}(pd.to_numeric({var_name}['{column_name}'], errors='coerce'))"
                )

            # Combine all operations into a single code block
            operation_code = "\n".join(operation_code_lines)

            # Execute the operation code using the execute_operation_code method
            result = self.execute_operation_code(operation_code, initial_namespace, df_name)

            if result['success']:
                # If execution was successful, update the dataframe with the result
                if var_name in result.get('dataframes', {}):
                    pandas_df.data = result['dataframes'][var_name]

                    # Create a description based on the number of columns
                    if len(column_names) == 1:
                        description = f"Calculate {config['description']} of '{column_names[0]}'"
                    else:
                        column_list = ", ".join([f"'{col}'" for col in column_names])
                        description = f"Calculate {config['description']} of multiple columns: {column_list}"

                    # Add the operation to the dataframe's history
                    pandas_df.add_operation(
                        code=operation_code,
                        operation_type="numpy",
                        description=description,
                        main_app=self.main_app
                    )

                    # Update the code manager and view
                    self.main_app.code_manager.update_code(df_name)
                    current_tab = self.main_app.df_viewer.currentWidget()
                    current_tab.model().update_data(pandas_df.data)

                    # Update status
                    if len(column_names) == 1:
                        self.main_app.status_bar.showMessage(f"Added {config['suffix']} column for '{column_names[0]}'")
                    else:
                        self.main_app.status_bar.showMessage(f"Added {config['suffix']} columns for {len(column_names)} selected columns")
                else:
                    self.main_app.status_bar.showMessage(f"Error: DataFrame not found in execution result")
            else:
                # If there was an error during execution, show it
                error_message = result.get('error', {}).get('message', 'Unknown error')
                self.main_app.status_bar.showMessage(f"Error calculating {config['suffix']}: {error_message}")
        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error calculating {config['suffix']}: {str(e)}")

    def numpy_log(self, df_name, column_indices):
        """
        Apply natural logarithm to one or more columns.

        Args:
            df_name: The dataframe name
            column_indices: A list of column indices to apply the operation to
        """
        self.apply_numpy_operation(df_name, column_indices, 'log')

    def numpy_exp(self, df_name, column_indices):
        """
        Apply exponential function to one or more columns.

        Args:
            df_name: The dataframe name
            column_indices: A list of column indices to apply the operation to
        """
        self.apply_numpy_operation(df_name, column_indices, 'exp')

    def numpy_sign(self, df_name, column_indices):
        """
        Apply sign function to one or more columns to extract the sign (-1, 0, or 1).

        Args:
            df_name: The dataframe name
            column_indices: A list of column indices to apply the operation to
        """
        self.apply_numpy_operation(df_name, column_indices, 'sign')

    def arithmetic_operation(self, df_name, visible_column_index, operation):
        """
        Apply an arithmetic operation to a column.

        Args:
            df_name: The dataframe name
            visible_column_index: The visible column index in the UI
            operation: The arithmetic operation to apply (+, -, *, /)
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)

        # Map the visible column index to the actual column index
        actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
        column_name = pandas_df.data.columns[actual_column_index]

        # Create and show the arithmetic operation dialog
        dialog = ArithmeticOperationDialog(
            parent=self.main_app,
            column_name=column_name,
            operation=operation,
            dataframe_columns=[col for col in pandas_df.data.columns]
        )
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            try:
                # Get the new column name from the dialog
                new_column_name = dialog.new_column_name

                # Create a valid Python variable name from the dataframe name
                var_name = df_name.replace('-', '_')

                # Get the operation code from the dialog
                operation_code = dialog.get_operation_code(var_name)

                # Execute the operation code using the execute_operation_code method
                initial_namespace = {var_name: pandas_df.data.copy()}
                result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                if result['success']:
                    # Update the dataframe with the result
                    if var_name in result['dataframes']:
                        pandas_df.data = result['dataframes'][var_name]
                    else:
                        self.main_app.status_bar.showMessage(f"Error: Operation did not produce expected dataframe")
                        return
                else:
                    self.main_app.status_bar.showMessage(f"Error executing operation: {result.get('error', {}).get('message', 'Unknown error')}")
                    return

                # Add operation code to track this operation
                pandas_df.add_operation(
                    code=operation_code,
                    operation_type="arithmetic",
                    description=f"Perform {operation} operation on '{column_name}'",
                    main_app=self.main_app
                )

                # Update the code manager and view
                self.main_app.code_manager.update_code(df_name)
                current_tab = self.main_app.df_viewer.currentWidget()
                current_tab.model().update_data(pandas_df.data)

                # Update status
                self.main_app.status_bar.showMessage(f"Added column '{new_column_name}'")
            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error performing arithmetic operation: {str(e)}")
                import traceback
                traceback.print_exc()

    def shift_column(self, df_name, visible_column_indices):
        """
        Apply a shift operation to one or more columns.

        Args:
            df_name: The dataframe name
            visible_column_indices: A list of visible column indices in the UI
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)

        # Ensure visible_column_indices is a list
        if not isinstance(visible_column_indices, list):
            visible_column_indices = [visible_column_indices]

        # Map the visible column indices to the actual column indices and get column names
        column_names = []
        for visible_idx in visible_column_indices:
            actual_idx = self._get_actual_column_index(df_name, visible_idx)
            column_names.append(pandas_df.data.columns[actual_idx])

        # Use different dialog based on number of columns
        if len(column_names) == 1:
            # Single column case - use the original dialog
            dialog = ShiftOperationDialog(self.main_app, column_names[0])
        else:
            # Multiple columns case - use the new multi-column dialog
            dialog = MultiShiftOperationDialog(self.main_app, column_names)

        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            try:
                # Get the periods value from the dialog
                periods = dialog.periods

                # Use proper dataframe variable name instead of generic 'df'
                var_name = df_name.replace('-', '_')

                # Prepare the initial namespace with the actual dataframe
                initial_namespace = {var_name: pandas_df.data}

                # For single column case
                if len(column_names) == 1:
                    column_name = column_names[0]
                    new_column_name = dialog.result_column_name

                    # Prepare the operation code with the proper variable name
                    operation_code = f"{var_name}['{new_column_name}'] = {var_name}['{column_name}'].shift({periods})"

                    # Execute the operation code
                    result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                    if result['success']:
                        # If execution was successful, update the dataframe with the result
                        if var_name in result.get('dataframes', {}):
                            pandas_df.data = result['dataframes'][var_name]

                        # Add the operation to the dataframe's history
                        pandas_df.add_operation(
                            code=operation_code,
                            operation_type="shift",
                            description=f"Shift '{column_name}' by {periods} periods",
                            main_app=self.main_app
                        )

                        # Update the code manager and view
                        self.main_app.code_manager.update_code(df_name)
                        current_tab = self.main_app.df_viewer.currentWidget()
                        current_tab.model().layoutChanged.emit()

                        # Update status
                        self.main_app.status_bar.showMessage(f"Added column '{new_column_name}' with {periods} period shift")
                    else:
                        # If there was an error during execution, show it
                        error_message = result.get('error', {}).get('message', 'Unknown error')
                        self.main_app.status_bar.showMessage(f"Error shifting column: {error_message}")

                # For multiple columns case
                else:
                    # Get the selected columns from the dialog
                    selected_columns = dialog.selected_columns

                    if not selected_columns:
                        self.main_app.status_bar.showMessage("No columns selected for shift operation")
                        return

                    # Build operation code for all selected columns
                    operation_code_lines = []
                    for column_name in selected_columns:
                        new_column_name = dialog.result_column_names[column_name]
                        operation_code_lines.append(
                            f"{var_name}['{new_column_name}'] = {var_name}['{column_name}'].shift({periods})"
                        )

                    # Combine all operations into a single code block
                    operation_code = "\n".join(operation_code_lines)

                    # Execute the operation code
                    result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                    if result['success']:
                        # If execution was successful, update the dataframe with the result
                        if var_name in result.get('dataframes', {}):
                            pandas_df.data = result['dataframes'][var_name]

                        # Create a description based on the number of columns
                        column_list = ", ".join([f"'{col}'" for col in selected_columns])
                        description = f"Shift multiple columns: {column_list} by {periods} periods"

                        # Add the operation to the dataframe's history
                        pandas_df.add_operation(
                            code=operation_code,
                            operation_type="shift",
                            description=description,
                            main_app=self.main_app
                        )

                        # Update the code manager and view
                        self.main_app.code_manager.update_code(df_name)
                        current_tab = self.main_app.df_viewer.currentWidget()
                        current_tab.model().layoutChanged.emit()

                        # Update status
                        self.main_app.status_bar.showMessage(f"Added {len(selected_columns)} shifted columns with {periods} period shift")
                    else:
                        # If there was an error during execution, show it
                        error_message = result.get('error', {}).get('message', 'Unknown error')
                        self.main_app.status_bar.showMessage(f"Error shifting columns: {error_message}")

            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error shifting column(s): {str(e)}")
                import traceback
                traceback.print_exc()

    def diff_column(self, df_name, visible_column_indices):
        """
        Apply diff operation to one or more columns to compute the difference between consecutive values.

        Args:
            df_name: The dataframe name
            visible_column_indices: A list of visible column indices in the UI
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)

        # Ensure visible_column_indices is a list
        if not isinstance(visible_column_indices, list):
            visible_column_indices = [visible_column_indices]

        # Map the visible column indices to the actual column indices and get column names
        column_names = []
        for visible_idx in visible_column_indices:
            actual_idx = self._get_actual_column_index(df_name, visible_idx)
            column_names.append(pandas_df.data.columns[actual_idx])

        # Use different dialog based on number of columns
        if len(column_names) == 1:
            # Single column case - use the original dialog
            dialog = DiffOperationDialog(self.main_app, column_names[0])
        else:
            # Multiple columns case - use the new multi-column dialog
            dialog = MultiDiffOperationDialog(self.main_app, column_names)

        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            try:
                # Get the periods value from the dialog
                periods = dialog.periods

                # Use proper dataframe variable name instead of generic 'df'
                var_name = df_name.replace('-', '_')

                # Prepare the initial namespace with the actual dataframe
                initial_namespace = {var_name: pandas_df.data}

                # For single column case
                if len(column_names) == 1:
                    column_name = column_names[0]
                    new_column_name = dialog.result_column_name

                    # Prepare the operation code with the proper variable name
                    operation_code = f"{var_name}['{new_column_name}'] = {var_name}['{column_name}'].diff({periods})"

                    # Execute the operation code
                    result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                    if result['success']:
                        # If execution was successful, update the dataframe with the result
                        if var_name in result.get('dataframes', {}):
                            pandas_df.data = result['dataframes'][var_name]

                        # Add the operation to the dataframe's history
                        pandas_df.add_operation(
                            code=operation_code,
                            operation_type="diff",
                            description=f"Calculate difference of '{column_name}' with {periods} period(s)",
                            main_app=self.main_app
                        )

                        # Update the code manager and view
                        self.main_app.code_manager.update_code(df_name)
                        current_tab = self.main_app.df_viewer.currentWidget()
                        current_tab.model().layoutChanged.emit()

                        # Update status
                        self.main_app.status_bar.showMessage(f"Added column '{new_column_name}' with {periods} period diff")
                    else:
                        # If there was an error during execution, show it
                        error_message = result.get('error', {}).get('message', 'Unknown error')
                        self.main_app.status_bar.showMessage(f"Error calculating diff: {error_message}")

                # For multiple columns case
                else:
                    # Get the selected columns from the dialog
                    selected_columns = dialog.selected_columns

                    if not selected_columns:
                        self.main_app.status_bar.showMessage("No columns selected for diff operation")
                        return

                    # Build operation code for all selected columns
                    operation_code_lines = []
                    for column_name in selected_columns:
                        new_column_name = dialog.result_column_names[column_name]
                        operation_code_lines.append(
                            f"{var_name}['{new_column_name}'] = {var_name}['{column_name}'].diff({periods})"
                        )

                    # Combine all operations into a single code block
                    operation_code = "\n".join(operation_code_lines)

                    # Execute the operation code
                    result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                    if result['success']:
                        # If execution was successful, update the dataframe with the result
                        if var_name in result.get('dataframes', {}):
                            pandas_df.data = result['dataframes'][var_name]

                        # Create a description based on the number of columns
                        column_list = ", ".join([f"'{col}'" for col in selected_columns])
                        description = f"Calculate difference of multiple columns: {column_list} with {periods} period(s)"

                        # Add the operation to the dataframe's history
                        pandas_df.add_operation(
                            code=operation_code,
                            operation_type="diff",
                            description=description,
                            main_app=self.main_app
                        )

                        # Update the code manager and view
                        self.main_app.code_manager.update_code(df_name)
                        current_tab = self.main_app.df_viewer.currentWidget()
                        current_tab.model().layoutChanged.emit()

                        # Update status
                        self.main_app.status_bar.showMessage(f"Added {len(selected_columns)} diff columns with {periods} period(s)")
                    else:
                        # If there was an error during execution, show it
                        error_message = result.get('error', {}).get('message', 'Unknown error')
                        self.main_app.status_bar.showMessage(f"Error calculating diff: {error_message}")

            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error calculating diff: {str(e)}")
                import traceback
                traceback.print_exc()

    def max_operation(self, df_name, visible_column_indices):
        """
        Calculate the maximum value across selected columns on axis=1 (row-wise maximum).

        Args:
            df_name: The dataframe name
            visible_column_indices: A list of visible column indices in the UI
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)
        if pandas_df is None:
            self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
            return

        # Ensure visible_column_indices is a list
        if not isinstance(visible_column_indices, list):
            visible_column_indices = [visible_column_indices]

        # Map the visible column indices to the actual column indices and get column names
        column_names = []
        for visible_idx in visible_column_indices:
            actual_idx = self._get_actual_column_index(df_name, visible_idx)
            column_names.append(pandas_df.data.columns[actual_idx])

        if len(column_names) < 1:
            self.main_app.status_bar.showMessage("No columns selected for max operation")
            return

        try:
            # Use proper dataframe variable name instead of generic 'df'
            var_name = df_name.replace('-', '_')

            # Create a new column name for the max result
            if len(column_names) == 1:
                new_column_name = f"{column_names[0]}_max"
            else:
                new_column_name = f"max_of_{len(column_names)}_columns"

            # Ensure the new column name is unique
            counter = 1
            original_name = new_column_name
            while new_column_name in pandas_df.data.columns:
                new_column_name = f"{original_name}_{counter}"
                counter += 1

            # Prepare the initial namespace with the actual dataframe
            initial_namespace = {var_name: pandas_df.data.copy()}

            # Create operation code to calculate max across selected columns on axis=1
            column_list = [f'{col}' for col in column_names]
            operation_code = f"{var_name}['{new_column_name}'] = {var_name}[{column_list}].max(axis=1)"

            # Execute the operation code
            result = self.execute_operation_code(operation_code, initial_namespace, df_name)

            if result['success']:
                # If execution was successful, update the dataframe with the result
                if var_name in result.get('dataframes', {}):
                    pandas_df.data = result['dataframes'][var_name]

                    # Create a description based on the number of columns
                    if len(column_names) == 1:
                        description = f"Calculate max value of '{column_names[0]}'"
                    else:
                        column_list_str = ", ".join([f"'{col}'" for col in column_names])
                        description = f"Calculate row-wise max across columns: {column_list_str}"

                    # Add the operation to the dataframe's history
                    pandas_df.add_operation(
                        code=operation_code,
                        operation_type="max",
                        description=description,
                        main_app=self.main_app
                    )

                    # Update the code manager and view
                    self.main_app.code_manager.update_code(df_name)
                    current_tab = self.main_app.df_viewer.currentWidget()
                    current_tab.model().update_data(pandas_df.data)

                    # Update status
                    if len(column_names) == 1:
                        self.main_app.status_bar.showMessage(f"Added max column '{new_column_name}' for '{column_names[0]}'")
                    else:
                        self.main_app.status_bar.showMessage(f"Added max column '{new_column_name}' across {len(column_names)} selected columns")
                else:
                    self.main_app.status_bar.showMessage(f"Error: DataFrame not found in execution result")
            else:
                # If there was an error during execution, show it
                error_message = result.get('error', {}).get('message', 'Unknown error')
                self.main_app.status_bar.showMessage(f"Error calculating max: {error_message}")

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error calculating max: {str(e)}")
            import traceback
            traceback.print_exc()

    def rolling_operation(self, df_name, visible_column_index, operation):
        """
        Apply a rolling window operation to a column and create a new column with the result.

        Args:
            df_name: The dataframe name
            visible_column_index: The visible column index in the UI
            operation: The rolling operation to apply (e.g., 'max', 'min')
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)

        # Map the visible column index to the actual column index
        actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
        column_name = pandas_df.data.columns[actual_column_index]

        # Create and show the rolling operation dialog
        dialog = RollingOperationDialog(self.main_app, column_name, operation)
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            try:
                # Get the window size value and result column name from the dialog
                window_size = dialog.window_size
                new_column_name = dialog.result_column_name

                # Use proper dataframe variable name instead of generic 'df'
                var_name = df_name.replace('-', '_')

                # Prepare the operation code with the proper variable name
                operation_code = f"{var_name}['{new_column_name}'] = {var_name}['{column_name}'].rolling(window={window_size}).{operation}()"

                # Prepare the initial namespace with the actual dataframe
                initial_namespace = {var_name: pandas_df.data}

                # Execute the operation code using the execute_operation_code method
                result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                if result['success']:
                    # If execution was successful, update the dataframe with the result
                    if var_name in result.get('dataframes', {}):
                        pandas_df.data = result['dataframes'][var_name]

                    # Add the operation to the dataframe's history
                    pandas_df.add_operation(
                        code=operation_code,
                        operation_type=f"rolling_{operation}",
                        description=f"Apply rolling {operation} with window size {window_size} to '{column_name}'",
                        main_app=self.main_app
                    )

                    # Update the code manager and view
                    self.main_app.code_manager.update_code(df_name)
                    current_tab = self.main_app.df_viewer.currentWidget()
                    current_tab.model().layoutChanged.emit()

                    # Update status
                    self.main_app.status_bar.showMessage(f"Added column '{new_column_name}' with rolling {operation} using window size {window_size}")
                else:
                    # If there was an error during execution, show it
                    error_message = result.get('error', {}).get('message', 'Unknown error')
                    self.main_app.status_bar.showMessage(f"Error calculating rolling {operation}: {error_message}")
            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error calculating rolling {operation}: {str(e)}")
                import traceback
                traceback.print_exc()

    def rolling_max(self, df_name, visible_column_index):
        """
        Apply rolling max to a column.
        """
        self.rolling_operation(df_name, visible_column_index, 'max')

    def rolling_min(self, df_name, visible_column_index):
        """
        Apply rolling min to a column.
        """
        self.rolling_operation(df_name, visible_column_index, 'min')

    def rolling_mean(self, df_name, visible_column_index):
        """
        Apply rolling mean to a column.
        """
        self.rolling_operation(df_name, visible_column_index, 'mean')

    def rolling_median(self, df_name, visible_column_index):
        """
        Apply rolling median to a column.
        """
        self.rolling_operation(df_name, visible_column_index, 'median')

    def groupby_transform(self, df_name, visible_column_index):
        """
        Apply a groupby transformation to a column.

        Args:
            df_name: The dataframe name
            visible_column_index: The visible column index in the UI to group by
        """
        pandas_df = self.main_app.df_manager.get_dataframe(df_name)

        # Map the visible column index to the actual column index
        actual_column_index = self._get_actual_column_index(df_name, visible_column_index)
        groupby_column_name = pandas_df.data.columns[actual_column_index]

        # Create and show the groupby transform dialog
        dialog = GroupbyTransformDialog(
            parent=self.main_app,
            groupby_column_name=groupby_column_name,
            dataframe_columns=[col for col in pandas_df.data.columns]
        )
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            try:
                # Get the transform parameters from the dialog
                transform_column = dialog.transform_column
                transform_func = dialog.transform_func
                new_column_name = dialog.get_new_column_name()

                # Create a valid Python variable name from the dataframe name
                var_name = df_name.replace('-', '_')

                # Get the operation code from the dialog
                operation_code = dialog.get_operation_code(var_name)

                # Prepare the initial namespace with the actual dataframe
                initial_namespace = {var_name: pandas_df.data}

                # Execute the operation code using the execute_operation_code method
                result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                if result['success']:
                    # If execution was successful, update the dataframe with the result
                    if var_name in result.get('dataframes', {}):
                        pandas_df.data = result['dataframes'][var_name]

                    # Add the operation to the dataframe's history
                    pandas_df.add_operation(
                        code=operation_code,
                        operation_type="groupby_transform",
                        description=f"Apply groupby transform on '{transform_column}' grouped by '{groupby_column_name}'",
                        main_app=self.main_app
                    )

                    # Update the code manager and view
                    self.main_app.code_manager.update_code(df_name)
                    current_tab = self.main_app.df_viewer.currentWidget()
                    current_tab.model().layoutChanged.emit()

                    # Update status
                    self.main_app.status_bar.showMessage(f"Added column '{new_column_name}' with groupby transform")
                else:
                    # If there was an error during execution, show it
                    error_message = result.get('error', {}).get('message', 'Unknown error')
                    self.main_app.status_bar.showMessage(f"Error applying groupby transform: {error_message}")
            except Exception as e:
                self.main_app.status_bar.showMessage(f"Error applying groupby transform: {str(e)}")
                import traceback
                traceback.print_exc()

    # Drop selected columns from the dataframe
    def drop_columns(self, df_name):
        try:
            # Get current table view and selected indexes
            current_tab = self.main_app.df_viewer.currentWidget()
            selected_indexes = current_tab.selectedIndexes()

            if not selected_indexes:
                self.main_app.status_bar.showMessage("No columns selected for dropping")
                return

            # Get unique visible column indexes
            visible_column_indexes = sorted(set(index.column() for index in selected_indexes))

            # Get the dataframe
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Map visible column indexes to actual column indexes
            actual_column_indexes = [self._get_actual_column_index(df_name, idx) for idx in visible_column_indexes]

            # Get column names for the actual column indexes
            column_names = [pandas_df.data.columns[idx] for idx in actual_column_indexes]

            # Show confirmation dialog
            column_list = ", ".join([f"'{col}'" for col in column_names])
            confirmation_message = f"Are you sure you want to drop the following column(s)?\n\n{column_list}"
            confirmation = QMessageBox.question(
                self.main_app,
                "Confirm Drop Columns",
                confirmation_message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if confirmation == QMessageBox.StandardButton.No:
                self.main_app.status_bar.showMessage("Drop columns operation cancelled")
                return

            # Create a valid Python variable name from the dataframe name
            var_name = f"{df_name.replace('-', '_')}"

            # Prepare the operation code with proper variable name
            operation_code = f"{var_name} = {var_name}.drop(columns={column_names})"

            # Prepare the initial namespace with the actual dataframe
            initial_namespace = {var_name: pandas_df.data}

            # Execute the operation code using the execute_operation_code method
            result = self.execute_operation_code(operation_code, initial_namespace, df_name)

            if result['success']:
                # If execution was successful, update the dataframe with the result
                if var_name in result.get('dataframes', {}):
                    pandas_df.data = result['dataframes'][var_name]

                # Add the operation to the dataframe's history
                drop_columns = [f"'{col}'" for col in column_names]
                pandas_df.add_operation(
                    code=operation_code,
                    operation_type="drop",
                    description=f"Drop columns: {', '.join(drop_columns)}",
                    main_app=self.main_app
                )

                # Update the code manager and view
                self.main_app.code_manager.update_code(df_name)
                current_tab = self.main_app.df_viewer.currentWidget()
                # current_tab.model().layoutChanged.emit()
                current_tab.model().update_data(pandas_df.data)

                # Update status
                self.main_app.status_bar.showMessage(f"Dropped {len(column_names)} column(s)")
            else:
                # If there was an error during execution, show it
                error_message = result.get('error', {}).get('message', 'Unknown error')
                self.main_app.status_bar.showMessage(f"Error dropping columns: {error_message}")
        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error dropping columns: {str(e)}")
            import traceback
            traceback.print_exc()

    # Drop selected rows from the dataframe
    def drop_rows(self, df_name):
        try:
            # Get current table view and selected indexes
            current_tab = self.main_app.df_viewer.currentWidget()
            selected_indexes = current_tab.selectedIndexes()

            if not selected_indexes:
                self.main_app.status_bar.showMessage("No rows selected for dropping")
                return

            # Get unique row indexes
            row_indexes = sorted(set(index.row() for index in selected_indexes))

            # Get the dataframe
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Show confirmation dialog
            row_count = len(row_indexes)
            confirmation_message = f"Are you sure you want to drop {row_count} row(s)?"
            confirmation = QMessageBox.question(
                self.main_app,
                "Confirm Drop Rows",
                confirmation_message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if confirmation == QMessageBox.StandardButton.No:
                self.main_app.status_bar.showMessage("Drop rows operation cancelled")
                return

            # Create a valid Python variable name from the dataframe name
            var_name = f"{df_name.replace('-', '_')}"

            # Prepare the operation code with proper variable name
            operation_code = f"{var_name} = {var_name}.drop(index={var_name}.index[{row_indexes}])"

            # Prepare the initial namespace with the actual dataframe
            initial_namespace = {var_name: pandas_df.data}

            # Execute the operation code using the execute_operation_code method
            result = self.execute_operation_code(operation_code, initial_namespace, df_name)

            if result['success']:
                # If execution was successful, update the dataframe with the result
                if var_name in result.get('dataframes', {}):
                    pandas_df.data = result['dataframes'][var_name]

                # Add the operation to the dataframe's history
                pandas_df.add_operation(
                    code=operation_code,
                    operation_type="drop",
                    description=f"Drop {len(row_indexes)} row(s)",
                    main_app=self.main_app
                )

                # Update the code manager and view
                self.main_app.code_manager.update_code(df_name)
                current_tab = self.main_app.df_viewer.currentWidget()
                # current_tab.model().layoutChanged.emit()
                current_tab.model().update_data(pandas_df.data)

                # Update status
                self.main_app.status_bar.showMessage(f"Dropped {len(row_indexes)} row(s)")
            else:
                # If there was an error during execution, show it
                error_message = result.get('error', {}).get('message', 'Unknown error')
                self.main_app.status_bar.showMessage(f"Error dropping rows: {error_message}")
        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error dropping rows: {str(e)}")
            import traceback
            traceback.print_exc()

    # Create a chart from the selected dataframe
    def create_chart(self, df_name, selected_column_index=None):
        """Create a chart from the selected dataframe

        Args:
            df_name: The name of the dataframe to create a chart from
            selected_column_index: The index of the currently selected column (0-based), if any
        """
        try:
            # Get the dataframe
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Show the chart dialog with tabs for different chart types
            chart_dialog = ChartDialog(self.main_app, df_name, selected_column_index)

            if chart_dialog.exec():
                # Get chart parameters from dialog
                chart_type = chart_dialog.chart_type
                x_column = chart_dialog.x_column
                y_column = chart_dialog.y_column
                y_columns = chart_dialog.y_columns
                bins = chart_dialog.bins
                title = chart_dialog.title or f"{df_name} - {chart_type} chart"

                # Validate inputs
                if not x_column:
                    self.main_app.status_bar.showMessage("Error: Must specify X-axis column")
                    return

                if chart_type == "scatter" and not y_column:
                    self.main_app.status_bar.showMessage("Error: Must specify Y-axis column for scatter charts")
                    return

                if chart_type == "line" and not y_columns:
                    self.main_app.status_bar.showMessage("Error: Must select at least one Y-axis column for line charts")
                    return

                # Create a valid Python variable name from the dataframe name
                var_name = df_name.replace('-', '_')

                # Generate matplotlib code based on chart type
                # First, add font configuration to support Chinese characters
                chart_code = f"import matplotlib.pyplot as plt\n"
                chart_code += f"import matplotlib.font_manager as fm\n"
                chart_code += f"import matplotlib.dates as mdates\n"
                chart_code += f"import platform\n"
                chart_code += f"import numpy as np\n"
                chart_code += f"import datetime\n"
                chart_code += f"from pandas.api.types import is_datetime64_any_dtype\n"

                # Add mplcursors for cross-hair functionality
                chart_code += f"try:\n"
                chart_code += f"    import mplcursors\n"
                chart_code += f"except ImportError:\n"
                chart_code += f"    import pip\n"
                chart_code += f"    pip.main(['install', 'mplcursors'])\n"
                chart_code += f"    import mplcursors\n\n"

                # Add helper function to check if a column contains datetime objects
                chart_code += f"def is_datetime_column(series):\n"
                chart_code += f"    # First check pandas dtype\n"
                chart_code += f"    if is_datetime64_any_dtype(series):\n"
                chart_code += f"        return True\n"
                chart_code += f"    \n"
                chart_code += f"    # If dtype is object, check the type of the first non-null value\n"
                chart_code += f"    if series.dtype == 'object':\n"
                chart_code += f"        for val in series:\n"
                chart_code += f"            if val is not None and val is not np.nan:\n"
                chart_code += f"                return isinstance(val, (datetime.datetime, datetime.date))\n"
                chart_code += f"    \n"
                chart_code += f"    return False\n\n"

                # Configure fonts based on the operating system
                chart_code += f"# Configure fonts to support Chinese characters\n"
                chart_code += f"if platform.system() == 'Windows':\n"
                chart_code += f"    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']\n"
                chart_code += f"elif platform.system() == 'Darwin':  # macOS\n"
                chart_code += f"    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti', 'Heiti TC', 'Arial Unicode MS', 'sans-serif']\n"
                chart_code += f"else:  # Linux and others\n"
                chart_code += f"    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Droid Sans Fallback', 'sans-serif']\n"
                chart_code += f"plt.rcParams['axes.unicode_minus'] = False  # Ensure minus sign is displayed correctly\n\n"

                if chart_type == "line":
                    chart_code += f"fig, ax = plt.subplots(figsize=(10, 6))\n"
                    chart_code += f"lines = []\n"  # Store line objects for cursor

                    # Add a plot line for each selected Y column
                    for y_col in y_columns:
                        chart_code += f"line, = ax.plot({var_name}['{x_column}'], {var_name}['{y_col}'], label='{y_col}')\n"
                        chart_code += f"lines.append(line)\n"

                    chart_code += f"ax.set_title('{title}')\n"
                    chart_code += f"ax.set_xlabel('{x_column}')\n"
                    chart_code += f"ax.set_ylabel('Values')\n"
                    chart_code += f"ax.legend()\n"  # Add legend to distinguish multiple lines
                    chart_code += f"ax.grid(True)\n\n"

                    # Format x-axis if it's a datetime
                    chart_code += f"# Format x-axis if it's a datetime\n"
                    chart_code += f"is_datetime = is_datetime_column({var_name}['{x_column}'])\n"
                    chart_code += f"if is_datetime:\n"
                    chart_code += f"    # Convert to datetime64 if it's a datetime.date or datetime.datetime object\n"
                    chart_code += f"    if {var_name}['{x_column}'].dtype == 'object':\n"
                    chart_code += f"        try:\n"
                    chart_code += f"            # Create a temporary series for plotting\n"
                    chart_code += f"            x_values = pd.Series(pd.to_datetime({var_name}['{x_column}']))\n"
                    chart_code += f"            # Redraw the lines with the converted x values\n"
                    chart_code += f"            ax.clear()\n"
                    chart_code += f"            lines.clear()\n"
                    chart_code += f"            for y_col in {y_columns}:\n"
                    chart_code += f"                line, = ax.plot(x_values, {var_name}[y_col], label=y_col)\n"
                    chart_code += f"                lines.append(line)\n"
                    chart_code += f"            ax.set_title('{title}')\n"
                    chart_code += f"            ax.set_xlabel('{x_column}')\n"
                    chart_code += f"            ax.set_ylabel('Values')\n"
                    chart_code += f"            ax.legend()\n"
                    chart_code += f"            ax.grid(True)\n"
                    chart_code += f"        except Exception as e:\n"
                    chart_code += f"            print(f'Warning: Could not convert object to datetime: {{e}}')\n"
                    chart_code += f"    \n"
                    chart_code += f"    date_format = mdates.DateFormatter('%Y-%m-%d')\n"
                    chart_code += f"    ax.xaxis.set_major_formatter(date_format)\n"
                    chart_code += f"    fig.autofmt_xdate()  # Rotate date labels\n\n"

                    # Add cross-hair cursor functionality
                    chart_code += f"# Add cross-hair cursor with data point values\n"
                    chart_code += f"cursor = mplcursors.cursor(lines, hover=True)\n"
                    chart_code += f"@cursor.connect('add')\n"
                    chart_code += f"def on_add(sel):\n"
                    chart_code += f"    line_index = lines.index(sel.artist)\n"
                    chart_code += f"    y_columns_list = {y_columns}\n"
                    chart_code += f"    y_col = y_columns_list[line_index]\n"
                    chart_code += f"    x, y = sel.target\n"
                    chart_code += f"    \n"
                    chart_code += f"    # Format x value based on its type\n"
                    chart_code += f"    if is_datetime:\n"
                    chart_code += f"        # Convert x (which is a float representing days since epoch) to a datetime\n"
                    chart_code += f"        try:\n"
                    chart_code += f"            x_date = mdates.num2date(x)\n"
                    chart_code += f"            x_formatted = x_date.strftime('%Y-%m-%d')\n"
                    chart_code += f"        except Exception as e:\n"
                    chart_code += f"            # Fallback to numeric format if conversion fails\n"
                    chart_code += f"            print(f'Warning: Could not format datetime: {{e}}')\n"
                    chart_code += f"            x_formatted = f'{{x:.4f}}'\n"
                    chart_code += f"    else:\n"
                    chart_code += f"        # For numeric x values, format with 4 decimal places\n"
                    chart_code += f"        x_formatted = f'{{x:.4f}}'\n"
                    chart_code += f"    \n"
                    chart_code += f"    sel.annotation.set_text(f'{x_column}: {{x_formatted}}\\n{{y_col}}: {{y:.4f}}')\n\n"

                    # Add cross-hair lines
                    chart_code += f"# Add cross-hair lines\n"
                    chart_code += f"def hover(event):\n"
                    chart_code += f"    if event.inaxes == ax:\n"
                    chart_code += f"        # Remove previous lines\n"
                    chart_code += f"        for artist in ax.get_children():\n"
                    chart_code += f"            if isinstance(artist, plt.Line2D) and artist.get_label() in ['h_line', 'v_line']:\n"
                    chart_code += f"                artist.remove()\n"
                    chart_code += f"        # Add new lines\n"
                    chart_code += f"        ax.axhline(y=event.ydata, color='gray', linestyle='--', alpha=0.5, label='h_line')\n"
                    chart_code += f"        ax.axvline(x=event.xdata, color='gray', linestyle='--', alpha=0.5, label='v_line')\n"
                    chart_code += f"        fig.canvas.draw_idle()\n\n"

                    chart_code += f"fig.canvas.mpl_connect('motion_notify_event', hover)\n"
                    chart_code += f"plt.show()"

                elif chart_type == "scatter":
                    chart_code += f"fig, ax = plt.subplots(figsize=(10, 6))\n"
                    chart_code += f"scatter = ax.scatter({var_name}['{x_column}'], {var_name}['{y_column}'])\n"
                    chart_code += f"ax.set_title('{title}')\n"
                    chart_code += f"ax.set_xlabel('{x_column}')\n"
                    chart_code += f"ax.set_ylabel('{y_column}')\n"
                    chart_code += f"ax.grid(True)\n\n"

                    # Format x-axis if it's a datetime
                    chart_code += f"# Check if columns contain datetime values\n"
                    chart_code += f"is_datetime_x = is_datetime_column({var_name}['{x_column}'])\n"
                    chart_code += f"is_datetime_y = is_datetime_column({var_name}['{y_column}'])\n"

                    chart_code += f"# Convert object columns with datetime objects to datetime64 for better plotting\n"
                    chart_code += f"x_values = {var_name}['{x_column}']\n"
                    chart_code += f"y_values = {var_name}['{y_column}']\n"

                    chart_code += f"if is_datetime_x and {var_name}['{x_column}'].dtype == 'object':\n"
                    chart_code += f"    try:\n"
                    chart_code += f"        x_values = pd.Series(pd.to_datetime({var_name}['{x_column}']))\n"
                    chart_code += f"    except Exception as e:\n"
                    chart_code += f"        print(f'Warning: Could not convert x column to datetime: {{e}}')\n"

                    chart_code += f"if is_datetime_y and {var_name}['{y_column}'].dtype == 'object':\n"
                    chart_code += f"    try:\n"
                    chart_code += f"        y_values = pd.Series(pd.to_datetime({var_name}['{y_column}']))\n"
                    chart_code += f"    except Exception as e:\n"
                    chart_code += f"        print(f'Warning: Could not convert y column to datetime: {{e}}')\n"

                    chart_code += f"# Redraw the scatter plot with possibly converted values\n"
                    chart_code += f"ax.clear()\n"
                    chart_code += f"scatter = ax.scatter(x_values, y_values)\n"
                    chart_code += f"ax.set_title('{title}')\n"
                    chart_code += f"ax.set_xlabel('{x_column}')\n"
                    chart_code += f"ax.set_ylabel('{y_column}')\n"
                    chart_code += f"ax.grid(True)\n"

                    chart_code += f"# Format axes if they contain datetime values\n"
                    chart_code += f"if is_datetime_x:\n"
                    chart_code += f"    date_format = mdates.DateFormatter('%Y-%m-%d')\n"
                    chart_code += f"    ax.xaxis.set_major_formatter(date_format)\n"
                    chart_code += f"    fig.autofmt_xdate()  # Rotate date labels\n"

                    chart_code += f"if is_datetime_y:\n"
                    chart_code += f"    date_format = mdates.DateFormatter('%Y-%m-%d')\n"
                    chart_code += f"    ax.yaxis.set_major_formatter(date_format)\n\n"

                    # Add cross-hair cursor functionality
                    chart_code += f"# Add cross-hair cursor with data point values\n"
                    chart_code += f"cursor = mplcursors.cursor(scatter, hover=True)\n"
                    chart_code += f"@cursor.connect('add')\n"
                    chart_code += f"def on_add(sel):\n"
                    chart_code += f"    x, y = sel.target\n"
                    chart_code += f"    \n"
                    chart_code += f"    # Format x value based on its type\n"
                    chart_code += f"    if is_datetime_x:\n"
                    chart_code += f"        # Convert x (which is a float representing days since epoch) to a datetime\n"
                    chart_code += f"        try:\n"
                    chart_code += f"            x_date = mdates.num2date(x)\n"
                    chart_code += f"            x_formatted = x_date.strftime('%Y-%m-%d')\n"
                    chart_code += f"        except Exception as e:\n"
                    chart_code += f"            # Fallback to numeric format if conversion fails\n"
                    chart_code += f"            print(f'Warning: Could not format x datetime: {{e}}')\n"
                    chart_code += f"            x_formatted = f'{{x:.4f}}'\n"
                    chart_code += f"    else:\n"
                    chart_code += f"        # For numeric x values, format with 4 decimal places\n"
                    chart_code += f"        x_formatted = f'{{x:.4f}}'\n"
                    chart_code += f"    \n"
                    chart_code += f"    # Format y value based on its type\n"
                    chart_code += f"    if is_datetime_y:\n"
                    chart_code += f"        # Convert y (which is a float representing days since epoch) to a datetime\n"
                    chart_code += f"        try:\n"
                    chart_code += f"            y_date = mdates.num2date(y)\n"
                    chart_code += f"            y_formatted = y_date.strftime('%Y-%m-%d')\n"
                    chart_code += f"        except Exception as e:\n"
                    chart_code += f"            # Fallback to numeric format if conversion fails\n"
                    chart_code += f"            print(f'Warning: Could not format y datetime: {{e}}')\n"
                    chart_code += f"            y_formatted = f'{{y:.4f}}'\n"
                    chart_code += f"    else:\n"
                    chart_code += f"        # For numeric y values, format with 4 decimal places\n"
                    chart_code += f"        y_formatted = f'{{y:.4f}}'\n"
                    chart_code += f"    \n"
                    chart_code += f"    sel.annotation.set_text(f'{x_column}: {{x_formatted}}\\n{y_column}: {{y_formatted}}')\n\n"

                    # Add cross-hair lines
                    chart_code += f"# Add cross-hair lines\n"
                    chart_code += f"def hover(event):\n"
                    chart_code += f"    if event.inaxes == ax:\n"
                    chart_code += f"        # Remove previous lines\n"
                    chart_code += f"        for artist in ax.get_children():\n"
                    chart_code += f"            if isinstance(artist, plt.Line2D) and artist.get_label() in ['h_line', 'v_line']:\n"
                    chart_code += f"                artist.remove()\n"
                    chart_code += f"        # Add new lines\n"
                    chart_code += f"        ax.axhline(y=event.ydata, color='gray', linestyle='--', alpha=0.5, label='h_line')\n"
                    chart_code += f"        ax.axvline(x=event.xdata, color='gray', linestyle='--', alpha=0.5, label='v_line')\n"
                    chart_code += f"        fig.canvas.draw_idle()\n\n"

                    chart_code += f"fig.canvas.mpl_connect('motion_notify_event', hover)\n"
                    chart_code += f"plt.show()"

                elif chart_type == "hist":
                    chart_code += f"plt.figure(figsize=(10, 6))\n"
                    chart_code += f"plt.hist({var_name}['{x_column}'], bins={bins})\n"
                    chart_code += f"plt.title('{title}')\n"
                    chart_code += f"plt.xlabel('{x_column}')\n"
                    chart_code += f"plt.ylabel('Frequency')\n"
                    chart_code += f"plt.grid(True)\n"
                    chart_code += f"plt.show()"

                # Set up the initial namespace with the dataframe
                initial_namespace = {var_name: pandas_df.data}

                # Execute the chart code
                self.main_app.status_bar.showMessage(f"Creating {chart_type} chart...")

                # Send the code to the Python interpreter for execution
                result = self.execute_operation_code(chart_code, initial_namespace)

                if not result['success']:
                    error_message = result.get('error', {}).get('message', 'Unknown error')
                    self.main_app.status_bar.showMessage(f"Error creating chart: {error_message}")
                else:
                    # Add the chart operation to the dataframe's history
                    if chart_type == "line":
                        # For line charts with multiple Y columns
                        y_cols_str = ", ".join([f"'{col}'" for col in y_columns])
                        description = f"Create {chart_type} chart of '{x_column}' vs {y_cols_str}"
                    elif chart_type == "scatter":
                        # For scatter charts
                        description = f"Create {chart_type} chart of '{x_column}' vs '{y_column}'"
                    else:  # hist
                        # For histogram charts
                        description = f"Create {chart_type} chart of '{x_column}' with {bins} bins"

                    pandas_df.add_operation(
                        code=chart_code,
                        operation_type=f"chart_{chart_type}",
                        description=description,
                        main_app=self.main_app
                    )

                    # Update the code manager
                    self.main_app.code_manager.update_code(df_name)

                    self.main_app.status_bar.showMessage(f"Created {chart_type} chart for {df_name}")

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error creating chart: {str(e)}")
            import traceback
            traceback.print_exc()

    def hide_columns(self, df_name):
        """
        Hide the selected columns in the dataframe view.

        Args:
            df_name: The dataframe name
        """
        try:
            # Get current table view and selected indexes
            current_tab = self.main_app.df_viewer.currentWidget()
            selected_indexes = current_tab.selectedIndexes()

            if not selected_indexes:
                self.main_app.status_bar.showMessage("No columns selected for hiding")
                return

            # Get unique column indexes
            column_indexes = sorted(set(index.column() for index in selected_indexes))

            # Get the PandasDataFrame
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if not pandas_df:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Get the model
            model = current_tab.model()

            # Map model column indices to actual dataframe column indices
            if hasattr(model, '_map_to_actual_column'):
                actual_column_indexes = [model._map_to_actual_column(col_idx) for col_idx in column_indexes]
            else:
                actual_column_indexes = column_indexes

            # Hide the columns in the PandasDataFrame
            pandas_df.hide_columns(actual_column_indexes)

            # Refresh the view
            model.beginResetModel()
            model.endResetModel()

            # Update status
            self.main_app.status_bar.showMessage(f"Hidden {len(column_indexes)} column(s)")

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error hiding columns: {str(e)}")
            import traceback
            traceback.print_exc()

    def show_all_columns(self, df_name):
        """
        Show all hidden columns in the dataframe view.

        Args:
            df_name: The dataframe name
        """
        try:
            # Get the PandasDataFrame
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if not pandas_df:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Get the number of hidden columns before showing them
            hidden_count = pandas_df.get_hidden_columns_count()

            # Show all columns
            pandas_df.show_all_columns()

            # Get current table view and refresh the model
            current_tab = self.main_app.df_viewer.currentWidget()
            model = current_tab.model()
            model.beginResetModel()
            model.endResetModel()

            # Update status
            self.main_app.status_bar.showMessage(f"Showing all {hidden_count} previously hidden column(s)")

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error showing columns: {str(e)}")
            import traceback
            traceback.print_exc()

    def show_dataframe_info(self, df_name):
        """
        Show information about the dataframe (dtypes and count).

        Args:
            df_name: The dataframe name
        """
        try:
            # Get the dataframe
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Create and show the info dialog
            info_dialog = InfoDialog(self.main_app, df_name)
            info_dialog.exec()

            # Update status
            self.main_app.status_bar.showMessage(f"Displayed info for dataframe '{df_name}'")

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error showing dataframe info: {str(e)}")
            import traceback
            traceback.print_exc()

    def merge_dataframes(self, df_name):
        """Merge the selected dataframe with another dataframe"""
        try:
            # Get the current dataframe
            pandas_df = self.main_app.df_manager.get_dataframe(df_name)
            if pandas_df is None:
                self.main_app.status_bar.showMessage(f"Error: Could not find dataframe '{df_name}'")
                return

            # Get all available dataframes excluding the current one
            all_dfs = self.main_app.df_manager.get_all_dataframes()
            available_df_names = [name for name in all_dfs.keys() if name != df_name]

            if not available_df_names:
                self.main_app.status_bar.showMessage("Error: No other dataframes available to merge with")
                return

            # Show merge dialog
            merge_dialog = MergeDialog(
                parent=self.main_app,
                available_dataframes=available_df_names,
                current_df_name=df_name
            )

            if merge_dialog.exec():
                # Get merge parameters from dialog
                right_df_name = merge_dialog.selected_dataframe
                how = merge_dialog.how
                left_on = [col.strip() for col in merge_dialog.left_on.split(',') if col.strip()]
                right_on = [col.strip() for col in merge_dialog.right_on.split(',') if col.strip()]
                suffixes = merge_dialog.suffixes
                indicator = merge_dialog.indicator
                selected_columns = merge_dialog.selected_columns

                # Validate inputs
                if not left_on or not right_on:
                    self.main_app.status_bar.showMessage("Error: Must specify columns to merge on")
                    return

                if len(left_on) != len(right_on):
                    self.main_app.status_bar.showMessage("Error: Number of columns must match for left and right dataframes")
                    return

                # Get the right dataframe
                right_pandas_df = self.main_app.df_manager.get_dataframe(right_df_name)

                # Generate proper variable names for the code
                left_var_name = f"df_{df_name.replace('-', '_')}"
                right_var_name = f"df_{right_df_name.replace('-', '_')}"
                result_name = f"{df_name}_merged_with_{right_df_name}"
                result_var_name = f"df_{result_name.replace('-', '_')}"

                # Check if we need to filter columns from the right dataframe
                if selected_columns and len(selected_columns) < len(right_pandas_df.data.columns):
                    # Make sure all right_on columns are included in selected_columns
                    for col in right_on:
                        if col not in selected_columns:
                            selected_columns.append(col)

                    # Create a temporary variable for the filtered right dataframe
                    temp_right_var = f"{right_var_name}_filtered"

                    # Create operation code with column filtering
                    operation_code = f"# Filter columns from right dataframe\n"
                    operation_code += f"{temp_right_var} = {right_var_name}[{selected_columns}]\n\n"
                    operation_code += f"# Perform merge operation\n"
                    operation_code += f"{result_var_name} = {left_var_name}.merge("
                    operation_code += f"\n    {temp_right_var},"
                    operation_code += f"\n    how='{how}',"
                    operation_code += f"\n    left_on={left_on},"
                    operation_code += f"\n    right_on={right_on},"
                    operation_code += f"\n    suffixes={suffixes},"
                    operation_code += f"\n    indicator={str(indicator)}"
                    operation_code += "\n)"

                    # Set up the initial namespace with the dataframes
                    initial_namespace = {
                        left_var_name: pandas_df.data,
                        right_var_name: right_pandas_df.data
                    }
                else:
                    # No column filtering needed, use the original merge code
                    operation_code = f"{result_var_name} = {left_var_name}.merge("
                    operation_code += f"\n    {right_var_name},"
                    operation_code += f"\n    how='{how}',"
                    operation_code += f"\n    left_on={left_on},"
                    operation_code += f"\n    right_on={right_on},"
                    operation_code += f"\n    suffixes={suffixes},"
                    operation_code += f"\n    indicator={str(indicator)}"
                    operation_code += "\n)"

                    # Set up the initial namespace with the dataframes
                    initial_namespace = {
                        left_var_name: pandas_df.data,
                        right_var_name: right_pandas_df.data
                    }

                # Execute the operation code using the execute_operation_code method
                result = self.execute_operation_code(operation_code, initial_namespace, df_name)

                if not result['success']:
                    error_message = result.get('error', {}).get('message', 'Unknown error')
                    self.main_app.status_bar.showMessage(f"Error merging dataframes: {error_message}")
                    return

                # Get the merged dataframe from the result
                merged_data = result['dataframes'].get(result_var_name)
                if merged_data is None:
                    self.main_app.status_bar.showMessage("Error: Merged dataframe not found in execution result")
                    return

                # Create a new dataframe with the merged result
                merged_df = self.main_app.df_manager.add_dataframe(
                    data=merged_data,
                    name=result_name,
                    source_path="merge operation",
                    parent=df_name
                )

                # Create a description that includes column selection information
                if selected_columns and len(selected_columns) < len(right_pandas_df.data.columns):
                    description = f"Merge with '{right_df_name}' using {how} join on {left_on} and {right_on} with selected columns"
                else:
                    description = f"Merge with '{right_df_name}' using {how} join on {left_on} and {right_on}"

                # Add operation to the new merged dataframe's history, not the parent
                merged_df.add_operation(
                    code=operation_code,
                    operation_type="merge",
                    description=description,
                    main_app=self.main_app
                )

                # Update the code manager for the new merged dataframe
                self.main_app.code_manager.update_code(result_name)

                # Switch to the new merged dataframe tab
                self.main_app.df_viewer.add_dataframe_tab(result_name)

                # Update status
                self.main_app.status_bar.showMessage(
                    f"Created merged dataframe '{result_name}' with shape {merged_data.shape}"
                )

        except Exception as e:
            self.main_app.status_bar.showMessage(f"Error merging dataframes: {str(e)}")
            import traceback
            traceback.print_exc()
