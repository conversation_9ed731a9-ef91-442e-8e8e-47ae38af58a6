from PyQt6.QtCore import Qt, QAbstractTableModel

class PandasTableModel(QAbstractTableModel):
    def __init__(self, data):
        super().__init__()
        self._data = data

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._data.columns)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole:
            value = self._data.iloc[index.row(), index.column()]
            return str(value)
        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return str(self._data.columns[section])
        if orientation == Qt.Orientation.Vertical and role == Qt.ItemDataRole.DisplayRole:
            return str(self._data.index[section])
        return None
        
    def update_data(self, new_data):
        """Update the model's data and emit appropriate signals.
        
        This method fully refreshes the model, including header data,
        which is crucial for column rename operations.
        """
        # Begin model reset to properly handle any structural changes
        self.beginResetModel()
        
        # Update internal data reference
        self._data = new_data
        
        # End model reset to notify the view that data has changed
        self.endResetModel()
