from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                            QListWidget, QPushButton, QDialogButtonBox, QMessageBox, 
                            QTextEdit, QWidget, QInputDialog)
from PyQt6.QtCore import Qt
from query import Query

class QueryDialog(QDialog):
    def __init__(self, parent, saved_queries=None):
        super().__init__(parent)
        self.parent = parent
        self.saved_queries = saved_queries or []  # List of Query objects
        self.selected_queries = []
        self.query_text = ""
        self.result_df_name = ""  # Store the result dataframe name
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Query DataFrame")
        self.setMinimumWidth(850)
        self.setMinimumHeight(500)

        main_layout = QHBoxLayout()  # Main layout is horizontal to split the dialog

        # Left panel for saved queries list
        left_panel = QVBoxLayout()

        saved_queries_label = QLabel("Saved Queries:")
        left_panel.addWidget(saved_queries_label)

        self.queries_list = QListWidget()
        # Populate the list with query names
        for query in self.saved_queries:
            self.queries_list.addItem(query.name)

        self.queries_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.queries_list.currentItemChanged.connect(self.display_selected_query)
        left_panel.addWidget(self.queries_list)

        # Query criteria display area
        criteria_label = QLabel("Query Criteria:")
        left_panel.addWidget(criteria_label)

        self.criteria_display = QTextEdit()
        self.criteria_display.setReadOnly(True)
        self.criteria_display.setMinimumHeight(100)
        left_panel.addWidget(self.criteria_display)

        # Buttons for saved queries
        saved_buttons_layout = QHBoxLayout()

        add_to_editor_btn = QPushButton("Add Selected to Editor")
        add_to_editor_btn.clicked.connect(self.add_selected_to_editor)
        saved_buttons_layout.addWidget(add_to_editor_btn)

        rename_query_btn = QPushButton("Rename Selected")
        rename_query_btn.clicked.connect(self.rename_selected_query)
        saved_buttons_layout.addWidget(rename_query_btn)

        delete_query_btn = QPushButton("Delete Selected")
        delete_query_btn.clicked.connect(self.delete_selected_queries)
        saved_buttons_layout.addWidget(delete_query_btn)

        left_panel.addLayout(saved_buttons_layout)

        # Add left panel to main layout
        left_panel_widget = QWidget()
        left_panel_widget.setLayout(left_panel)
        main_layout.addWidget(left_panel_widget)

        # Right panel for query editor and save options
        right_panel = QVBoxLayout()

        # Result dataframe name section (moved to the top)
        result_name_layout = QHBoxLayout()
        result_name_label = QLabel("Result DataFrame Name:")
        result_name_layout.addWidget(result_name_label)

        self.result_name_edit = QLineEdit()
        self.result_name_edit.setPlaceholderText("Name for the query result dataframe")
        result_name_layout.addWidget(self.result_name_edit)

        right_panel.addLayout(result_name_layout)

        # Add some spacing
        right_panel.addSpacing(10)

        # Query editor section
        editor_label = QLabel("Query Editor (one expression per line):")
        right_panel.addWidget(editor_label)

        # Change to QTextEdit for multiple lines
        self.query_editor = QTextEdit()
        self.query_editor.setPlaceholderText("Enter query expressions (e.g., 'column > 5')")
        self.query_editor.setMinimumHeight(150)
        right_panel.addWidget(self.query_editor)

        # Save query section with name
        save_section = QVBoxLayout()

        # Query name input
        query_name_layout = QHBoxLayout()
        query_name_label = QLabel("Query Name:")
        query_name_layout.addWidget(query_name_label)

        self.query_name_edit = QLineEdit()
        self.query_name_edit.setPlaceholderText("Enter a name for this query")
        query_name_layout.addWidget(self.query_name_edit)

        save_section.addLayout(query_name_layout)

        # Save button
        save_btn = QPushButton("Save Current Query")
        save_btn.clicked.connect(self.save_current_query)
        save_section.addWidget(save_btn)

        right_panel.addLayout(save_section)

        # Dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        right_panel.addWidget(button_box)

        # Add right panel to main layout
        right_panel_widget = QWidget()
        right_panel_widget.setLayout(right_panel)
        main_layout.addWidget(right_panel_widget)

        self.setLayout(main_layout)

    def display_selected_query(self, current, _):
        """Display the criteria of the selected query"""
        if not current:
            self.criteria_display.clear()
            return

        selected_name = current.text()

        # Find the query with the selected name
        for query in self.saved_queries:
            if query.name == selected_name:
                self.criteria_display.setPlainText(query.criteria)
                return

        self.criteria_display.clear()

    def add_selected_to_editor(self):
        selected_items = self.queries_list.selectedItems()
        if not selected_items:
            return

        queries_to_add = []
        for item in selected_items:
            selected_name = item.text()

            # Find the query criteria for the selected name
            for query in self.saved_queries:
                if query.name == selected_name:
                    queries_to_add.append(query.criteria)
                    break

        if not queries_to_add:
            return

        current_text = self.query_editor.toPlainText()
        if current_text and not current_text.endswith('\n'):
            current_text += "\n"

        self.query_editor.setPlainText(current_text + "\n".join(queries_to_add))

    def rename_selected_query(self):
        """Rename the selected query"""
        selected_items = self.queries_list.selectedItems()
        if not selected_items or len(selected_items) != 1:
            # Only allow renaming one query at a time
            QMessageBox.warning(self, "Warning", "Please select exactly one query to rename.")
            return

        selected_item = selected_items[0]
        selected_name = selected_item.text()

        # Find the query with the selected name
        selected_query = None
        selected_index = -1

        for i, query in enumerate(self.saved_queries):
            if query.name == selected_name:
                selected_query = query
                selected_index = i
                break

        if selected_query is None:
            return

        # Show dialog to get new name
        new_name, ok = QInputDialog.getText(
            self,
            "Rename Query",
            "Enter new name for the query:",
            QLineEdit.EchoMode.Normal,
            selected_name
        )

        if not ok or not new_name.strip() or new_name == selected_name:
            return

        # Check if the new name already exists
        for query in self.saved_queries:
            if query.name == new_name and query != selected_query:
                QMessageBox.warning(self, "Warning", f"A query with the name '{new_name}' already exists.")
                return

        # Update the query with the new name
        self.saved_queries[selected_index] = Query(
            name=new_name,
            criteria=selected_query.criteria,
            description=selected_query.description
        )

        # Update the list widget
        self.queries_list.clear()
        for query in self.saved_queries:
            self.queries_list.addItem(query.name)

        # Select the renamed item
        for i in range(self.queries_list.count()):
            if self.queries_list.item(i).text() == new_name:
                self.queries_list.setCurrentRow(i)
                break

    def delete_selected_queries(self):
        selected_items = self.queries_list.selectedItems()
        if not selected_items:
            return

        for item in selected_items:
            selected_name = item.text()

            # Find and remove the query with the selected name
            for i, query in enumerate(self.saved_queries):
                if query.name == selected_name:
                    self.saved_queries.pop(i)
                    break

        # Update the list widget
        self.queries_list.clear()
        for query in self.saved_queries:
            self.queries_list.addItem(query.name)

        # Clear the criteria display
        self.criteria_display.clear()

    def save_current_query(self):
        query_text = self.query_editor.toPlainText().strip()
        query_name = self.query_name_edit.text().strip()

        if not query_text:
            return

        # Use a default name if none provided
        if not query_name:
            # Generate a default name based on the first line of the query
            first_line = query_text.split('\n')[0]
            query_name = f"Query: {first_line[:30]}"
            if len(first_line) > 30:
                query_name += "..."

        # Check if a query with this name already exists
        for i, query in enumerate(self.saved_queries):
            if query.name == query_name:
                # Update existing query
                self.saved_queries[i] = Query(query_name, query_text)
                break
        else:
            # Add new query
            self.saved_queries.append(Query(query_name, query_text))

        # Update the list widget
        self.queries_list.clear()
        for query in self.saved_queries:
            self.queries_list.addItem(query.name)

        # Clear the name field
        self.query_name_edit.clear()

    def accept(self):
        self.query_text = self.query_editor.toPlainText()
        self.result_df_name = self.result_name_edit.text()
        super().accept()
